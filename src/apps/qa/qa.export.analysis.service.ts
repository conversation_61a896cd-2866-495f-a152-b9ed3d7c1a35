import { BadRequestException, Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BatchDiligenceEntity } from '../../libs/entities/BatchDiligenceEntity';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { EntityManager, In, Repository } from 'typeorm';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { PlatformUser } from '../../libs/model/common';
import * as _ from 'lodash';
import { v1 as uuidv1 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { RiskModelService } from '../risk_model/risk_model.service';
import MyOssService from '../basic/my-oss.service';
import { ConfigService } from '../../libs/config/config.service';
import { InjectRepository } from '@nestjs/typeorm';
import { QaTaskEntity } from '../../libs/entities/QaTaskEntity';
import * as Bluebird from 'bluebird';
import { CompanyDetailService } from '../company/company-detail.service';
import { StrategyRoleEnums } from '../../libs/enums/StrategyRoleEnums';

@Injectable()
export class QaExportAnalysisService {
  private readonly logger: Logger = QccLogger.getLogger(QaExportAnalysisService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly entityManager: EntityManager,
    private readonly riskModelService: RiskModelService,
    private readonly myOssService: MyOssService,
    private readonly companyDetailService: CompanyDetailService,
    @InjectRepository(QaTaskEntity) private readonly qaTaskRepo: Repository<QaTaskEntity>,
  ) {}

  /**
   * 根据批次尽调结果生成统计数据并导出CSV文件
   * @param batchId 批次ID
   * @returns {Promise<{success: boolean, filePath: string, fileName: string}>}
   */
  public async generateBatchDilligenceStatistic(taskId: number) {
    // 获取task
    const task = await this.qaTaskRepo.findOne({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      throw new BadRequestException('任务不存在');
    }
    const batchId = task.refDiligenceBatchIdBase;
    // 1. 获取尽调数据
    const batchDiligenceList = await this.entityManager.find(BatchDiligenceEntity, { batchId });
    if (!batchDiligenceList?.length) {
      return { success: false, message: '未找到批次相关的尽调数据' };
    }
    const diligenceIds = batchDiligenceList.map((item) => item.diligenceId);
    const diligenceList = await this.entityManager.find(DiligenceHistoryEntity, { id: In(diligenceIds) });
    if (!diligenceList?.length) {
      return { success: false, message: '未找到尽调历史数据' };
    }
    const riskModel = await this.entityManager.findOne(RiskModelEntity, { modelId: diligenceList[0].orgModelId });
    if (!riskModel) {
      return { success: false, message: '未找到模型数据' };
    }
    // 2. 获取风险模型详情
    const model = await this.getRiskModelDetails(riskModel.modelId, riskModel.orgId, riskModel.product);
    if (!model) {
      return { success: false, message: '未找到风险模型数据' };
    }
    // 3. 处理模型命中策略信息
    const modelHitStrategyInfos = this.extractModelHitStrategyInfos(model);
    // 4. 处理尽调命中数据
    const dilligenceHitStrategyInfos = await this.extractDiligenceHitStrategyInfos(diligenceList);

    // 5. 生成CSV文件
    const result = await this.generateCsvFile(batchId, modelHitStrategyInfos, dilligenceHitStrategyInfos);
    return result;
  }

  /**
   * 获取风险模型详情
   */
  private async getRiskModelDetails(orgModelId: number, orgId: number, product: string) {
    return this.riskModelService.getRiskModelFullDetails(
      orgModelId,
      Object.assign(new PlatformUser(), {
        currentOrg: orgId,
        currentProduct: product,
      }),
    );
  }

  /**
   * 提取模型命中策略信息
   */
  private extractModelHitStrategyInfos(model: any): Array<{
    metricName: string;
    metricsId: number;
    strategyId: number;
    strategyName: string;
    maxScore: number;
  }> {
    const modelHitStrategyInfos = [];
    model.groups.forEach((group) => {
      group.groupMetrics.forEach((metric) => {
        const { name: metricName, metricsId, hitStrategy, dimensionHitStrategies } = metric.metricEntity;
        // 根据order排序
        const sortedHitStrategies = hitStrategy.sort((a, b) => a.order - b.order);
        sortedHitStrategies.forEach((strategy) => {
          const processStrategyList = (strategyList: number[]) => {
            if (!strategyList?.length) return;
            strategyList.forEach((strategyId) => {
              const strategyInfo = dimensionHitStrategies.find((t) => t.dimensionStrategyId === strategyId);
              if (strategyInfo && strategyInfo.dimensionHitStrategyEntity.strategyRole !== StrategyRoleEnums.OnlyFilter) {
                modelHitStrategyInfos.push({
                  metricName,
                  metricsId,
                  strategyId,
                  strategyName: strategyInfo.dimensionHitStrategyEntity.strategyName,
                  maxScore: strategy.scoreSettings.maxScore,
                });
              }
            });
          };
          // 处理must、should、must_not策略
          processStrategyList(strategy.must);
          processStrategyList(strategy.should);
          processStrategyList(strategy.must_not);
        });
      });
    });
    return modelHitStrategyInfos;
  }

  /**
   * 提取尽调命中数据
   */
  public async extractDiligenceHitStrategyInfos(diligenceList: DiligenceHistoryEntity[]): Promise<
    Array<{
      companyId: string;
      companyName: string;
      qccScore: number;
      metricName: string;
      metricsId: number;
      strategyId: number;
      strategyName: string;
      hitScore: number;
    }>
  > {
    const hitStrategyInfos = [];
    diligenceList.forEach((diligence) => {
      const { companyId, name: companyName, details } = diligence;
      details.originalHits.forEach((hit) => {
        const { metricsId, name: metricName, hitDetails } = hit;
        const { must = [], should = [], must_not = [] } = hitDetails.hitStrategy || {};

        const processHitList = (hitList: number[], strategies: any[]) => {
          hitList.forEach((strategyId) => {
            const strategyInfo = strategies.find((t) => t.strategyId === strategyId);
            if (strategyInfo) {
              hitStrategyInfos.push({
                companyId,
                companyName,
                metricName,
                metricsId,
                strategyId,
                strategyName: strategyInfo?.strategyName,
                hitScore: hitDetails.hitStrategy.scoreSettings.maxScore,
              });
            }
          });
        };

        processHitList(must, hitDetails.must);
        processHitList(should, hitDetails.should);
        processHitList(must_not, hitDetails.must_not);
      });
    });
    if (hitStrategyInfos?.length) {
      await Bluebird.map(
        hitStrategyInfos,
        async (hitStrategyInfo) => {
          const result = await this.companyDetailService.GetTechRate(hitStrategyInfo.companyId);
          hitStrategyInfo.qccScore = result?.Result?.TotalScore || 0;
        },
        { concurrency: 1 },
      );
    }
    return hitStrategyInfos;
  }

  /**
   * 生成CSV文件
   */
  private async generateCsvFile(
    batchId: number,
    modelHitStrategyInfos: any[],
    dilligenceHitStrategyInfos: any[],
  ): Promise<{ success: boolean; filePath?: string; fileName?: string; message?: string; error?: string; downloadUrl?: string }> {
    try {
      // 1. 生成表头
      const headers = [
        '企业名称',
        '企查查科创分',
        '风险洞察模型科创分',
        ...modelHitStrategyInfos.map((info) => `【${info.metricName}】【${info.strategyName}】【${info.maxScore}】`),
      ];
      // 2. 按企业ID分组处理数据
      const groupedData = _.groupBy(dilligenceHitStrategyInfos, 'companyId');
      // 3. 生成CSV内容
      const csvRows = [headers.join(',')];
      Object.entries(groupedData).forEach(([companyId, companyData]) => {
        const row = new Array(headers.length).fill('');
        row[0] = companyData[0]?.companyName;
        row[1] = companyData[0]?.qccScore;
        let totalScore = 0;
        companyData.forEach((hit) => {
          const strategyInfo = modelHitStrategyInfos.find((info) => info.metricsId === hit.metricsId && info.strategyId === hit.strategyId);
          if (strategyInfo) {
            const columnIndex = headers.findIndex(
              (header) => header === `【${strategyInfo.metricName}】【${strategyInfo.strategyName}】【${strategyInfo.maxScore}】`,
            );
            if (columnIndex !== -1) {
              const score = hit.hitScore;
              row[columnIndex] = score.toString();
              totalScore += score;
            }
          }
        });
        row[2] = totalScore.toString();
        csvRows.push(row.join(','));
      });

      // 4. 生成文件
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `diligence_statistic_${batchId}_${timestamp}.csv`;
      const exportDir = path.join(process.cwd(), 'exports');
      const filePath = path.join(exportDir, fileName);

      // 确保导出目录存在
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      // 写入文件（添加BOM头支持中文）
      fs.writeFileSync(filePath, '\ufeff' + csvRows.join('\n'));

      // 5. 上传文件到OSS
      try {
        const ossFileName = `${uuidv1()}.csv`;
        const ossObject = this.configService.getOssObject('diligence_statistic', ossFileName);
        const options = {
          headers: {
            'Content-Disposition': `attachment;filename=${encodeURIComponent(fileName)};filename*=UTF-8''${encodeURIComponent(fileName)}`,
          },
        };
        const ossResult = await this.myOssService.putSteam(ossObject, filePath, options);

        // 6. 删除本地临时文件
        fs.unlinkSync(filePath);

        return {
          success: true,
          fileName,
          downloadUrl: this.myOssService.signSingleUrl(ossResult),
        };
      } catch (ossError) {
        this.logger.error('上传文件到OSS失败:', ossError);
        return {
          success: false,
          message: '上传文件到OSS失败',
          error: ossError.message,
        };
      }
    } catch (error) {
      this.logger.error('生成CSV文件失败:', error);
      return {
        success: false,
        message: '生成CSV文件失败',
        error: error.message,
      };
    }
  }
}
