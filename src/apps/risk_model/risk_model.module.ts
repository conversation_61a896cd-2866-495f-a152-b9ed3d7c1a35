import { Module } from '@nestjs/common';
import { RiskModelController } from './risk_model.controller';
import { RiskModelService } from './risk_model.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { DistributedSystemResourceEntity } from '../../libs/entities/DistributedSystemResourceEntity';
import { GroupMetricRelationEntity } from '../../libs/entities/GroupMetricRelationEntity';
import { MetricDimensionRelationEntity } from '../../libs/entities/MetricDimensionRelationEntity';
import { RiskModelExternalController } from './risk_model.external.controller';
import { RiskModelInitService } from './init_mode/risk_model.init.service';
import { DimensionModule } from '../dimension/dimension.module';
import { ModelInitMonitorService } from './init_mode/model.init.monitor.service';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { ModelInitMonitorSellerService } from './init_mode/model.init.monitor.seller.service';
import { RiskModelInitBocLuService } from './init_mode/risk_model.init.boc.lu.service';
import { ModelInitICBCSZMonitorService } from './init_mode/model.init.monitor.icbc.sz.service';
import { RiskModelSTIHealthService } from './init_mode/risk_model.init.STI.Indicators.service';
import { RiskModelICBCSZService } from "./init_mode/risk_model.init.icbc.sz.service";

@Module({
  controllers: [RiskModelController, RiskModelExternalController],
  providers: [
    RiskModelService,
    RiskModelInitService,
    ModelInitMonitorService,
    ModelInitMonitorSellerService,
    RiskModelInitBocLuService,
    ModelInitICBCSZMonitorService,
    RiskModelSTIHealthService,
    RiskModelICBCSZService,
  ],
  imports: [
    TypeOrmModule.forFeature([RiskModelEntity, MonitorGroupEntity, DistributedSystemResourceEntity, GroupMetricRelationEntity, MetricDimensionRelationEntity]),
    DimensionModule,
  ],
  exports: [
    RiskModelInitService,
    RiskModelService,
    ModelInitMonitorService,
    ModelInitMonitorSellerService,
    RiskModelInitBocLuService,
    ModelInitICBCSZMonitorService,
    RiskModelSTIHealthService,
    RiskModelICBCSZService,
  ],
})
export class RiskModelModule {}
