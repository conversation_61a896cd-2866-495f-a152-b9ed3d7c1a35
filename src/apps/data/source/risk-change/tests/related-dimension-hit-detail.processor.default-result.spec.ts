import { Test, TestingModule } from '@nestjs/testing';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import { PersonHelper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createMockStrategyField = (
  fieldKey: DimensionFieldKeyEnums,
  fieldValue: any[],
  compareType?: DimensionFieldCompareTypeEnums,
): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  field.compareType = compareType;
  return field;
};

describe('RelatedDimensionHitDetailProcessor 默认维度和结果处理测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getFinalActualController: jest.fn(),
      getPartnerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getInvestCompany: jest.fn(),
    } as any;

    mockCompanySearchService = {
      companyDetailsQcc: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      commonCivilRiskChange: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      detailAnalyzeForRelated: jest.fn(),
      hitIndustryThresholdField: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      getCaseTitleDescData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('默认维度处理 (default case)', () => {
    it('应该正确处理未知维度类型', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      const mockEsData = [
        { id: 'es1', KeyNo: 'person1', CreateDate: 1640995200000 },
        { id: 'es2', KeyNo: 'person1', CreateDate: 1641081600000 },
      ];
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 2 },
            hits: mockEsData.map((d) => ({ _source: d })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockEsData);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockRiskChangeHelper.detailAnalyzeForRelated).toHaveBeenCalledWith(mockEsData, dimension, params);
      expect(result.Paging.TotalRecords).toBe(2);
      expect(result.Result).toHaveLength(2);
    });

    it('应该正确处理默认维度无命中数据的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(result.Result).toBeUndefined();
    });
  });

  describe('结果处理和分页', () => {
    it('应该正确处理分页逻辑', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果 - 10条数据
      const mockEsData = Array.from({ length: 10 }, (_, i) => ({
        id: `es${i + 1}`,
        KeyNo: 'person1',
        CreateDate: 1640995200000 + i * 86400000, // 每天递增
        ObjectId: `obj${i + 1}`,
        ChangeExtend: `change${i + 1}`,
      }));

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 10 },
            hits: mockEsData.map((d) => ({ _source: d })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockEsData);

      // Mock getRiskListDesc 函数
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      jest.spyOn(require('apps/data/risk.copy.from.c/risk'), 'getRiskListDesc').mockImplementation((item: any) => ({
        Title: '',
        Subtitle: '',
        Content: '',
        ContentArray: [],
        Highlight: [],
        OtherHighlight: [],
        SubtitleHighlight: '',
        RelateChange: '',
        Category: item.Category,
        Id: item.id,
        Name: item.Name,
        ObjectId: item.ObjectId,
        ChangeDate: null,
        CreateDate: item.CreateDate,
        UpdateDate: null,
        PublishTime: item.CreateDate,
        ChangeExtend: item.ChangeExtend,
        processed: true,
      }));

      // Mock getCaseTitleDescData
      mockCaseReasonHelper.getCaseTitleDescData.mockResolvedValue(undefined);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 2, // 第二页
        pageSize: 3, // 每页3条
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(10);
      expect(result.Result).toHaveLength(3); // 第二页应该有3条数据
      // 验证分页逻辑：第二页应该是索引3-5的数据（按CreateDate降序排列后）
      expect(result.Result[0].Id).toBeDefined();
    });

    it('应该正确处理按CreateDate降序排序', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果 - 乱序的时间数据
      const mockEsData = [
        { id: 'es1', CreateDate: 1640995200000 }, // 2022-01-01
        { id: 'es2', CreateDate: 1672531200000 }, // 2023-01-01
        { id: 'es3', CreateDate: 1609459200000 }, // 2021-01-01
      ];

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: mockEsData.map((d) => ({ _source: d })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockEsData);

      // Mock getCaseTitleDescData
      mockCaseReasonHelper.getCaseTitleDescData.mockResolvedValue(undefined);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Result).toHaveLength(3);
      // 验证按CreateDate降序排列：2023 > 2022 > 2021
      expect(result.Result[0].CreateDate).toBe(1672531200000); // 2023-01-01
      expect(result.Result[1].CreateDate).toBe(1640995200000); // 2022-01-01
      expect(result.Result[2].CreateDate).toBe(1609459200000); // 2021-01-01
    });

    it('应该正确处理ChangeExtend映射', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      const mockEsData = [
        { id: 'es1', ObjectId: 'obj1', ChangeExtend: 'change1', CreateDate: 1640995200000 },
        { id: 'es2', ObjectId: 'obj1', ChangeExtend: 'change2', CreateDate: 1640995300000 },
        { id: 'es3', ObjectId: 'obj2', ChangeExtend: 'change3', CreateDate: 1640995400000 },
      ];

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: mockEsData.map((d) => ({ _source: d })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockEsData);

      // Mock getCaseTitleDescData
      mockCaseReasonHelper.getCaseTitleDescData.mockResolvedValue(undefined);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      await processor.fetchHits(dimension, params);

      // Assert
      expect(mockCaseReasonHelper.getCaseTitleDescData).toHaveBeenCalledWith(expect.any(Array), false, {
        obj1: ['change1', 'change2'],
        obj2: ['change3'],
      });
    });

    it('应该正确处理空结果的情况', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 1,
        pageSize: 10,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(result.Result).toBeUndefined();
      expect(mockCaseReasonHelper.getCaseTitleDescData).not.toHaveBeenCalled();
    });

    it('应该正确处理超出页面范围的分页请求', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy('UnknownDimension' as DimensionTypeEnums);
      dimension.strategyFields = [
        createMockStrategyField(DimensionFieldKeyEnums.relatedRoleType, [RelatedTypeEnums.ActualController]),
        createMockStrategyField(DimensionFieldKeyEnums.riskCategories, ['category1']),
      ];

      // Mock 实际控制人
      mockPersonHelper.getFinalActualController.mockResolvedValue([{ keyNo: 'person1', name: '实控人1' }]);

      // Mock ES查询结果 - 只有3条数据
      const mockEsData = Array.from({ length: 3 }, (_, i) => ({
        id: `es${i + 1}`,
        CreateDate: 1640995200000 + i * 86400000,
      }));

      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 3 },
            hits: mockEsData.map((d) => ({ _source: d })),
          },
        },
      });

      // Mock 关联方过滤
      mockRiskChangeHelper.detailAnalyzeForRelated.mockResolvedValue(mockEsData);

      const params: HitDetailsBaseQueryParams = {
        keyNo: 'test-company-id',
        pageIndex: 5, // 请求第5页，但只有3条数据
        pageSize: 2,
      };

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(3);
      expect(result.Result).toHaveLength(0); // 超出范围，返回空数组
    });
  });
});
