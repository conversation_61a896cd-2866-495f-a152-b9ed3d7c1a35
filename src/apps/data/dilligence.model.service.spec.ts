import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../app/app.test.module';
import { DataModule } from './data.module';
import { RiskChangeEsSource } from './source/risk-change/risk-change-es.source';
import { JudgementSource } from './source/judgement.source';
import { getDimensionHitStrategyPO } from '../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from '../../libs/model/diligence/details/request';
import { TargetInvestigationEnums } from '../../libs/enums/dimension/FieldValueEnums';
import { EnterpriseLibApiSource } from './source/enterprise-lib-api.source';
import { CreditEsSource } from './source/credit-es.source';
import { SupervisePunishEsSource } from './source/supervise-punish-es.source';
import { CompanyApiSource } from './source/company-api.source';
import { QccProApiSource } from './source/qcc-pro-api.source';
import { AllTopicTypes } from '../../libs/constants/news.constants';
import { NegativeNewsSource } from './source/negative-news.source';
import { OuterBlacklistSource } from './source/outer-blacklist.source';
import { RelatedCompanySource } from './source/related-company.source';
import { PledgeSource } from './source/pledge.source';
import { ViolationSource } from './source/violation.source';
import { RelatedTypeEnums } from '../../libs/enums/dimension/RelatedTypeEnums';
import { NebulaRelatedEdgeEnums } from '../../libs/enums/dimension/NebulaRelatedEdgeEnums';
import { CreditApiSource } from './source/credit-api.source';
import { OvsSanctionsBlacklistSource } from './source/ovs-sanctions-blacklist.source';
import { TenderApiSource } from './source/tender-api.source';
import { TaxEsSource } from './source/tax-es.source';
import { CaseSource } from './source/case.source';

jest.setTimeout(60 * 10000);
describe('尽调模型集成测试', () => {
  let caseService: CaseSource;
  let companyService: CompanyApiSource;
  //未实现
  let creditApiService: CreditApiSource;
  let creditESService: CreditEsSource;
  let enterpriseService: EnterpriseLibApiSource;
  let judgementService: JudgementSource;
  let nebulaGraphService: RelatedCompanySource;
  let negativeNewsService: NegativeNewsSource;
  let outerBlacklistService: OuterBlacklistSource;
  let ovsSanctionsService: OvsSanctionsBlacklistSource;
  let pledgeService: PledgeSource;
  let proService: QccProApiSource;
  let riskChangeService: RiskChangeEsSource;
  let supervisePunishService: SupervisePunishEsSource;
  let taxService: TaxEsSource;
  let tenderService: TenderApiSource;
  let violationService: ViolationSource;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    riskChangeService = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    judgementService = module.get<JudgementSource>(JudgementSource);
    enterpriseService = module.get<EnterpriseLibApiSource>(EnterpriseLibApiSource);
    creditESService = module.get<CreditEsSource>(CreditEsSource);
    supervisePunishService = module.get<SupervisePunishEsSource>(SupervisePunishEsSource);
    companyService = module.get<CompanyApiSource>(CompanyApiSource);
    proService = module.get<QccProApiSource>(QccProApiSource);
    negativeNewsService = module.get<NegativeNewsSource>(NegativeNewsSource);
    outerBlacklistService = module.get<OuterBlacklistSource>(OuterBlacklistSource);
    nebulaGraphService = module.get<RelatedCompanySource>(RelatedCompanySource);
    pledgeService = module.get<PledgeSource>(PledgeSource);
    violationService = module.get<ViolationSource>(ViolationSource);
    ovsSanctionsService = module.get<OvsSanctionsBlacklistSource>(OvsSanctionsBlacklistSource);
    tenderService = module.get<TenderApiSource>(TenderApiSource);
    taxService = module.get<TaxEsSource>(TaxEsSource);
    caseService = module.get<CaseSource>(CaseSource);
  });

  it('曾发生刑事案件', async () => {
    const companyId = 'a99f766d973f4a8b4da8d0ebd6294e34';
    const companyName = '麻城市华祥再生资源循环利用有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      // 案件类型 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        fieldValue: ['defendant'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近5年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('公安通告涉嫌非法吸收公众存款或非法集资的企业', async () => {
    const companyId = 'a52a3447c7a6df44c8a3f8698b572dc9';
    const companyName = '成都钱乾信息技术有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SecurityNotice, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【法定代表人失信风险】-限制高消费-当前法人', async () => {
    const companyId = 'a2168498281775c6eebd6eede4dd5dd8';
    const companyName = '北京百乐文化传媒有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Legal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人失信风险】-限制出境-当前法人', async () => {
    const companyId = '1a94e9513a670bada13140583d87990e';
    const companyName = '上海兴闳房地产开发有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersRestrictedOutbound, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Legal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人失信风险】-失信被执行人-当前法人', async () => {
    const companyId = 'a2168498281775c6eebd6eede4dd5dd8';
    const companyName = '北京百乐文化传媒有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Legal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人失信风险】-限制高消费-历史法人', async () => {
    const companyId = 'bb0b89e9b9ceabefdeb9c639b9d69a51';
    const companyName = '北京九乐玖美容有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.HisLegal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人失信风险】-限制出境-历史法人', async () => {
    const companyId = 'da3036a4f7f9672fbc0f52479b5346af';
    const companyName = '宁波国美美乐科技有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersRestrictedOutbound, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.HisLegal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人失信风险】-失信被执行人-历史法人', async () => {
    const companyId = 'bb0b89e9b9ceabefdeb9c639b9d69a51';
    const companyName = '北京九乐玖美容有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.HisLegal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人失信风险】-限制高消费-当前法人-近五年', async () => {
    const companyId = 'cf9481cc65ef335818cc15d89da10701';
    const companyName = '南宁市典藏商贸有限责任公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionHistory, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Legal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // it('【法定代表人失信风险】-限制出境-当前法人-近五年', async () => {
  //     const companyId = '1a94e9513a670bada13140583d87990e';
  //     const companyName = '上海兴闳房地产开发有限公司';
  //
  //     const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersRestrictedOutbound, [
  //         {
  //             fieldKey: DimensionFieldKeyEnums.targetInvestigation,
  //             accessScope: 2,
  //             fieldValue: [TargetInvestigationEnums.Legal],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.sortField,
  //             accessScope: 1,
  //             fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //     ]);
  //     const detail = await creditESService.analyze(companyId, [dimension]);
  //     expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
  //     const result = await creditESService.getDimensionDetail(
  //         dimension,
  //         Object.assign(
  //             new HitDetailsBaseQueryParams(),
  //             {
  //                 keyNo: companyId,
  //                 pageIndex: 1,
  //                 pageSize: 10,
  //             },
  //             {keyNo: companyId, companyName},
  //         ),
  //     );
  //     expect(result).not.toBeNull();
  //     expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  // });

  // it('【法定代表人失信风险】-失信被执行人-当前法人-近五年', async () => {
  //     const companyId = 'e38bbc7f3a1a1161e2ace52f90cda585';
  //     const companyName = '中科高新安徽售电有限公司';
  //
  //     const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditHistory, [
  //         {
  //             fieldKey: DimensionFieldKeyEnums.targetInvestigation,
  //             accessScope: 2,
  //             fieldValue: [TargetInvestigationEnums.Legal],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.cycle,
  //             fieldValue: [5],
  //             options: [-1, 1, 3, 5],
  //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.sortField,
  //             accessScope: 1,
  //             fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //     ]);
  //     // const detail = await enterpriseService.analyze(companyId, [dimension]);
  //     // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
  //     const result = await enterpriseService.getDimensionDetail(
  //         dimension,
  //         Object.assign(
  //             new HitDetailsBaseQueryParams(),
  //             {
  //                 keyNo: companyId,
  //                 pageIndex: 1,
  //                 pageSize: 10,
  //             },
  //             {keyNo: companyId, companyName},
  //         ),
  //     );
  //     expect(result).not.toBeNull();
  //     expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  // });

  it('【法定代表人失信风险】-限制高消费-历史法人-近五年', async () => {
    const companyId = 'bb0b89e9b9ceabefdeb9c639b9d69a51';
    const companyName = '北京九乐玖美容有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RestrictedConsumptionHistory, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.HisLegal],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // it('【法定代表人失信风险】-限制出境-历史法人-近五年', async () => {
  //     const companyId = 'da3036a4f7f9672fbc0f52479b5346af';
  //     const companyName = '宁波国美美乐科技有限公司';
  //
  //     const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersRestrictedOutbound, [
  //         {
  //             fieldKey: DimensionFieldKeyEnums.targetInvestigation,
  //             accessScope: 2,
  //             fieldValue: [TargetInvestigationEnums.HisLegal],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.isValid,
  //             accessScope: 1,
  //             fieldValue: [0],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.cycle,
  //             fieldValue: [5],
  //             options: [-1, 1, 3, 5],
  //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.sortField,
  //             accessScope: 1,
  //             fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //     ]);
  //     const detail = await creditESService.analyze(companyId, [dimension]);
  //     expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
  //     const result = await creditESService.getDimensionDetail(
  //         dimension,
  //         Object.assign(
  //             new HitDetailsBaseQueryParams(),
  //             {
  //                 keyNo: companyId,
  //                 pageIndex: 1,
  //                 pageSize: 10,
  //             },
  //             {keyNo: companyId, companyName},
  //         ),
  //     );
  //     expect(result).not.toBeNull();
  //     expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  // });

  // it('【法定代表人失信风险】-失信被执行人-历史法人-近五年', async () => {
  //     const companyId = 'bb0b89e9b9ceabefdeb9c639b9d69a51';
  //     const companyName = '北京九乐玖美容有限公司';
  //
  //     const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditHistory, [
  //         {
  //             fieldKey: DimensionFieldKeyEnums.targetInvestigation,
  //             accessScope: 2,
  //             fieldValue: [TargetInvestigationEnums.Legal],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.cycle,
  //             fieldValue: [5],
  //             options: [-1, 1, 3, 5],
  //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
  //         },
  //         {
  //             fieldKey: DimensionFieldKeyEnums.sortField,
  //             accessScope: 1,
  //             fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
  //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
  //         },
  //     ]);
  //     const detail = await enterpriseService.analyze(companyId, [dimension]);
  //     expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
  //     const result = await enterpriseService.getDimensionDetail(
  //         dimension,
  //         Object.assign(
  //             new HitDetailsBaseQueryParams(),
  //             {
  //                 keyNo: companyId,
  //                 pageIndex: 1,
  //                 pageSize: 10,
  //             },
  //             {keyNo: companyId, companyName},
  //         ),
  //     );
  //     expect(result).not.toBeNull();
  //     expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  // });

  it('【被列入失信被执行人】-失信被执行人-企业本身', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditCurrent, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入失信被执行人】-失信被执行人（历史）-企业本身', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonCreditHistory, [
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.Self],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近5年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【税收违法】-税收违法', async () => {
    const companyId = 'f6cd38cc4196e58c98ad8c72396e384c';
    const companyName = '湛江市富晟石化有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxationOffences, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【税收违法】-税务处罚', async () => {
    const companyId = 'ee17584b5c4ef35901dccc77039d335d';
    const companyName = '广西宽度企业管理咨询有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxPenalties, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await supervisePunishService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await supervisePunishService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【案件风险】', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        accessScope: 2,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【案件风险（历史）】', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      // 统计周期 3年前
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        accessScope: 2,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【开庭公告】-工商基本信息', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['70', '80', '99', '90', '75', '85'],
        accessScope: 2,
        options: [
          { name: '停业', value: '70' },
          { name: '撤销', value: '80' },
          { name: '注销', value: '99' },
          { name: '吊销', value: '90' },
          { name: '歇业', value: '75' },
          { name: '责令关闭', value: '85' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
    ]);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【开庭公告】-开庭公告', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CourtSessionAnnouncement, [
      // 统计周期 近3年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 有效
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'liandate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【开庭公告（补充）】-工商基本信息', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['70', '80', '99', '90', '75', '85'],
        accessScope: 2,
        options: [
          { name: '停业', value: '70' },
          { name: '撤销', value: '80' },
          { name: '注销', value: '99' },
          { name: '吊销', value: '90' },
          { name: '歇业', value: '75' },
          { name: '责令关闭', value: '85' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
    ]);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【开庭公告（补充）】-开庭公告（补充）', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CourtSessionAnnouncement, [
      // 统计周期 近3年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 无效
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'liandate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【审查调查】-该企业主要人员有执纪审查或党纪政务处分信息', async () => {
    const companyId = '76cec7d1849950214408d6e04bb8a718';
    const companyName = '海南省盐业集团有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ReviewAndInvestigation);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await proService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入被执行人】-被执行人', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonExecution, [
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      /* {
              fieldKey: DimensionFieldKeyEnums.cycle,
              fieldValue: [1],
              compareType: DimensionFieldCompareTypeEnums.GreaterThan,
            },*/
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入被执行人】-被执行人（历史）', async () => {
    const companyId = 'c0db5ad18141ac64892ed2671a3e5a32';
    const companyName = '奥途（北京）二手车鉴定评估有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.PersonExecution, [
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      /*{
              fieldKey: DimensionFieldKeyEnums.cycle,
              fieldValue: [1],
              compareType: DimensionFieldCompareTypeEnums.GreaterThan,
            },*/
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入严重违法失信企业名录】-被列入严重违法失信企业名录', async () => {
    const companyId = '679a2914296eb18462ea4beab32872b2';
    const companyName = '深圳市捷皇新建筑装饰设计工程有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyCredit, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'AddDate', order: 'DESC', fieldSnapshot: 'AddDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入严重违法失信企业名录】-被列入严重违法失信企业名录（历史）', async () => {
    const companyId = '46da34ef7fd69e06589f422a327aab6b';
    const companyName = '陕西通家汽车股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyCreditHistory, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'AddDate', order: 'DESC', fieldSnapshot: 'AddDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入经营异常名录】-被列入经营异常名录', async () => {
    const companyId = '030f162fa99546ec9dad51e75ac14538';
    const companyName = '西安正太玉器珠宝有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal3, [
      {
        // 经营异常类型
        fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
        fieldValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 周期不限
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        // 排序
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [
          {
            field: 'occurrencedate',
            order: 'DESC',
            fieldSnapshot: 'CurrenceDate',
          },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入经营异常名录（历史）】-被列入经营异常名录（历史）', async () => {
    const companyId = '37fc4eed1383edb39a18f5d2af91bb4e';
    const companyName = '海南澜博规划咨询有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OperationAbnormal, [
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [0],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        // 周期
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'occurrencedate', order: 'DESC', fieldSnapshot: 'CurrenceDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【被列入税务非正常户】-被列入税务非正常户', async () => {
    const companyId = 'e4f619bf95c1c6a648b34b0955af8465';
    const companyName = '福建丰益进出口贸易有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxUnnormals, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【监管处罚】', async () => {
    const companyId = 'eab195aa40a0d459eeb9b3fdad22deba';
    const companyName = '中喜会计师事务所（特殊普通合伙）';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AdministrativePenalties, [
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 周期 近3年
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        // 处罚单位
        fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
        accessScope: 2,
        fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
        options: [
          { label: '人民银行', value: '1' },
          { label: '证监会', value: '2' },
          { label: '金融监管局', value: '3' },
          { label: '交易所', value: '4' },
          { label: '发改委', value: '7' },
          { label: '工信部', value: '8' },
          { label: '财政部', value: '9' },
          { label: '网信办', value: '10' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await supervisePunishService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await supervisePunishService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【行政处罚】', async () => {
    const companyId = '7854a22a33def639a128f61a843fc2e1';
    const companyName = '青岛矢和精工汽车配件有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AdministrativePenalties, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltiesType,
        fieldValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909', '0999'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 处罚单位
        fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
        accessScope: 1,
        fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
        options: [
          { label: '人民银行', value: '1' },
          { label: '证监会', value: '2' },
          { label: '金融监管局', value: '3' },
          { label: '交易所', value: '4' },
          { label: '发改委', value: '7' },
          { label: '工信部', value: '8' },
          { label: '财政部', value: '9' },
          { label: '网信办', value: '10' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await supervisePunishService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await supervisePunishService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【工商抽查检查不合格】', async () => {
    const companyId = '23d4bc98b08f6d5dc7873ecb42b6f556';
    const companyName = '西安市灞桥区东海气体站';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SpotCheck, [
      {
        // 周期
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [
          {
            field: 'publishdate',
            order: 'DESC',
            fieldSnapshot: 'PublishDate',
          },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【环保处罚】', async () => {
    const companyId = 'dfa490c6dfd92dda6047124830952200';
    const companyName = '深圳市满圆春劳务派遣有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EnvironmentalPenalties, [
      {
        // 周期
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【经营状态非存续】', async () => {
    const companyId = '92baadfb4287ffab43731f0c159b038f';
    const companyName = '广州市穗城房地产开发有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal1);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【简易注销】', async () => {
    const companyId = '013f78767e30bfd0974df8006e230fd9';
    const companyName = '成都慧眼之家科技有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal2);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【注销备案】', async () => {
    const companyId = '03cad68532a08f69d4e3aa4ac410fc12';
    const companyName = '儋州亮的房地产有限责任公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CancellationOfFiling);
    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【经营期限已过有效期】', async () => {
    const companyId = '01bfdd398efeba75bcf791cd7e83177d';
    const companyName = '吉林省华建工程设备有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal6);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【企业存在涉诈风险】', async () => {
    const companyId = 'd6435e7b6d3bc097ee1e86a297530c1e';
    const companyName = '大连强志永发装修工程有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        accessScope: 1,
        fieldValue: ['1310'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【支付/融资担保业务被中止或注销】', async () => {
    const companyId = '19e1ab87c42f5c765aa62f55ae451949';
    const companyName = '上海瑞得企业服务有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk1312);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await proService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【假冒国企】', async () => {
    const companyId = 'edba440ce1c5fb0dc244ac026f0a988f';
    const companyName = '中稳能源产业发展（海南）有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.FakeSOES);
    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【疑似空壳企业】', async () => {
    // 测试数据
    const companyId = 'ae531b6860f4b5594c45d5896bdbd5d6'; // 广州元来酒店管理有限公司
    const companyName = '广州元来酒店管理有限公司';

    // 获取维度策略PO
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyShell);

    // 执行分析
    const detail = await companyService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取Pro服务的维度详情
    const proDetail = await proService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言Pro服务的详情结果
    expect(proDetail).not.toBeNull();
    expect(proDetail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //db填充数据
  it.skip('【人员存在违规处理事项】', async () => {
    // 测试数据
    const companyId = 'abd9a5892bc7bdafbaabb5f1c70a09ce'; // 广州元来酒店管理有限公司
    const companyName = '广州市时瑞置业有限公司';

    // 获取维度策略PO
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ViolationProcessings, [
      // 与处罚主体关系类型
      {
        fieldKey: DimensionFieldKeyEnums.relationShips,
        fieldValue: ['1', '3', '5', '7', '9'],
        options: [
          { value: '1', label: '法定代表人' },
          { value: '3', label: '董监高' },
          { value: '5', label: '股东' },
          { value: '7', label: '实际控制人' },
          { value: '9', label: '受益自然人' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近三年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publicdate', order: 'DESC', fieldSnapshot: 'publicdate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 执行分析
    const detail = await violationService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取Pro服务的维度详情
    const proDetail = await violationService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言Pro服务的详情结果
    expect(proDetail).not.toBeNull();
    expect(proDetail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //db填充数据
  it.skip('【人员存在违规处理事项】-历史人员存在违规处理', async () => {
    // 测试数据
    const companyId = '106bc9009d4313b9b44ad676638472ff'; // 广州元来酒店管理有限公司
    const companyName = '海口佩克石油化工有限公司';

    // 获取维度策略PO
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ViolationProcessings, [
      // 与处罚主体关系类型
      {
        fieldKey: DimensionFieldKeyEnums.relationShips,
        fieldValue: ['2', '4', '6'],
        options: [
          { value: '2', label: '历史法定代表人' },
          { value: '4', label: '历史董监高' },
          { value: '6', label: '历史股东' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近三年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publicdate', order: 'DESC', fieldSnapshot: 'publicdate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 执行分析
    const detail = await violationService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取Pro服务的维度详情
    const proDetail = await violationService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言Pro服务的详情结果
    expect(proDetail).not.toBeNull();
    expect(proDetail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【人员存在刑事案件】', async () => {
    // 测试数据
    const companyId = '106bc9009d4313b9b44ad676638472ff';
    const companyName = '海口佩克石油化工有限公司';

    // 获取维度策略PO
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      // 排查对象 主要人员(在职)
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [
          TargetInvestigationEnums.MainStaff,
          TargetInvestigationEnums.Legal,
          TargetInvestigationEnums.ActualController,
          TargetInvestigationEnums.Benefit,
        ],
        options: [
          { label: '董监高', value: TargetInvestigationEnums.MainStaff },
          { label: '法定代表人', value: TargetInvestigationEnums.Legal },
          { label: '实际控制人', value: TargetInvestigationEnums.ActualController },
          { label: '受益自然人', value: TargetInvestigationEnums.Benefit },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案件类型 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        fieldValue: ['defendant'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 不限
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 执行分析
    const detail = await judgementService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【人员存在刑事案件】-历史人员存在刑事案件', async () => {
    // 测试数据
    const companyId = '97feb16c599b943a49a82068b5dae80e';
    const companyName = '安徽协鑫矿业技术开发有限公司';

    // 获取维度策略PO
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Judgement, [
      // 排查对象
      {
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        accessScope: 2,
        fieldValue: [TargetInvestigationEnums.HisMainStaff, TargetInvestigationEnums.HisLegal],
        options: [
          { label: '历史董监高', value: TargetInvestigationEnums.HisMainStaff },
          { label: '历史法定代表人', value: TargetInvestigationEnums.HisLegal },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由类别 刑事案件
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        accessScope: 2,
        fieldValue: ['xs'],
        options: [{ label: '刑事案件', value: 'xs' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        fieldValue: ['defendant'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 统计周期 近三年
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      // 数据范围 不限
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 执行分析
    const detail = await judgementService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【分支机构中企业存在异常】-分支机构企业异常-吊销', async () => {
    // 测试数据
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        options: [{ label: '分支机构', value: RelatedTypeEnums.Branch }],
        accessScope: 1,
        fieldValue: [RelatedTypeEnums.Branch],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        options: [{ name: '吊销', value: '90' }],
        accessScope: 2,
        fieldValue: ['90'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 执行分析
    const detail = await nebulaGraphService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await nebulaGraphService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【分支机构中企业存在异常】-分支机构企业异常-经营异常，严重违法，失信被执行人，税收违法', async () => {
    // 测试数据
    const companyId = 'b4fafa7884961982aeec55b61029ca9c';
    const companyName = '南阳市双汇食品销售有限责任公司';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SeriousViolation, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        options: [{ label: '分支机构', value: RelatedTypeEnums.Branch }],
        accessScope: 1,
        fieldValue: [RelatedTypeEnums.Branch],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [
          { label: '经营异常', value: 'Exception' },
          { label: '严重违法', value: 'SeriousViolation' },
          { label: '失信被执行人', value: 'BadCreditExecuted' },
          { label: '税收违法', value: 'TaxIllegal' },
        ],
        accessScope: 2,
        fieldValue: ['Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 执行分析
    const detail = await nebulaGraphService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await nebulaGraphService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【分支机构中企业存在异常】-分支机构企业异常(历史)-经营异常，严重违法，失信被执行人，税收违法', async () => {
    // 测试数据
    const companyId = 'ed30daf496c0326b2a409831d99ff160';
    const companyName = '南阳市双汇食品销售有限责任公司';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SeriousViolation, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        options: [{ label: '分支机构', value: RelatedTypeEnums.Branch }],
        accessScope: 1,
        fieldValue: [RelatedTypeEnums.Branch],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [
          { label: '经营异常', value: 'Exception' },
          { label: '严重违法', value: 'SeriousViolation' },
          { label: '失信被执行人', value: 'BadCreditExecuted' },
          { label: '税收违法', value: 'TaxIllegal' },
        ],
        accessScope: 2,
        fieldValue: ['Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 执行分析
    const detail = await nebulaGraphService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await nebulaGraphService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【负面新闻】', async () => {
    // 测试数据
    const companyId = '93cbf5ef4aa867781c8a0ce7d5789eb1'; // 深圳市宝能投资集团有限公司
    const companyName = '深圳市宝能投资集团有限公司';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.NegativeNews, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        // 新闻主体类型： 停工停产，生产事故，偷税漏税
        fieldKey: DimensionFieldKeyEnums.topics,
        fieldValue: ['all'],
        options: AllTopicTypes,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'publishtime' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 执行分析
    const detail = await negativeNewsService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await negativeNewsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【无统一社会信用代码】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal7, []);

    // 执行分析
    const detail = await companyService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【临近经营期限】', async () => {
    // 测试数据
    const companyId = 'gff5aa61547dd8281a80dd71915eb4b8'; // 天津市仁爱实业集团有限公司
    const companyName = '固原市原州区供销合作社联合社';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal8, []);

    // 执行分析
    const detail = await companyService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【受益所有人无法识别或穿透边界以外】', async () => {
    // 测试数据
    const companyId = 'eec622cb45a23dc3134839297db3e1bc';
    const companyName = '临洮县瑞民种植农民专业合作社';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        accessScope: 1,
        fieldValue: ['6601'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 执行分析
    const detail = await companyService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【法定代表人控制企业域名被列入涉赌涉诈黑名单】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 获取维度策略PO，与createMetric中的fields保持一致
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        accessScope: 1,
        fieldValue: ['6708'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 执行分析
    const detail = await companyService.analyze(companyId, [dimension]);

    // 断言分析结果
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );

    // 断言详情结果
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【分支机构域名被列入涉赌涉诈黑名单】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        fieldValue: ['6808'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0]?.totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人控制企业涉及高风险行业】', async () => {
    // 测试数据
    const companyId = 'cf4c8d940d8d0fd76ca9a66b2faadfb5';
    const companyName = '盱眙玖盛昌金属材料有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6710, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'startDate', order: 'DESC', fieldSnapshot: 'startDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      companyName,
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【涉及高风险行业】', async () => {
    // 测试数据
    const companyName = '广州天河区忠揩商贸有限公司';
    const companyId = 'a6bba67b2c9ea1cfb0aefdb4f0f73392';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        accessScope: 1,
        fieldValue: ['6313'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【破产重整】', async () => {
    // 测试数据
    const companyId = '4ef60c4aa9d8cb72eff475e47172b0af';
    const companyName = '陕西新颐田实业有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Bankruptcy, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'riskdate', order: 'DESC', fieldSnapshot: 'RiskDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await creditESService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【破产重整（历史）】', async () => {
    // 测试数据
    const companyId = 'ba2d8331838e69a4f0f9a6580c4d99e4';
    const companyName = '神州长城国际工程有限公司';
    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Bankruptcy, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'riskdate', order: 'DESC', fieldSnapshot: 'RiskDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await creditESService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权冻结】', async () => {
    // 测试数据
    const companyId = 'baccfa38cdbb680588c1fd033c37c6ca';
    const companyName = '深圳龙懿新能源科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.FreezeEquity, [
      {
        fieldKey: DimensionFieldKeyEnums.equityAmount,
        fieldValue: [0],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LianDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await creditESService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await creditESService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【联系方式异常】', async () => {
    // 测试数据
    const companyId = '43601d703d66406158471453db6c9944';
    const companyName = '成都胡固巴电子商务有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        fieldValue: ['6401', '6402', '6404'],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【员工数据不明】', async () => {
    // 测试数据
    const companyId = 'e04571de4deb9089073d4772c8711808';
    const companyName = '上海胤瑞石油设备有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6302, []);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权出质】', async () => {
    // 测试数据
    const companyId = '89afa39b2ef53f5d564e077b54e6c5be';
    const companyName = '山东郓城农村商业银行股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EquityPledge, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'RegDate' }],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await pledgeService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await pledgeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人变更】-近期法定代表人变更', async () => {
    // 测试数据
    const companyId = '90f9f13d2c9a1f6b0c8a5e8f75671ed2';
    const companyName = '深圳瑞贤金嘉网络科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateLegalPerson, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //近期法定代表人变更覆盖
  it.skip('【法定代表人变更】-近期法定代表人频繁变更', async () => {
    // 测试数据
    const companyId = '90f9f13d2c9a1f6b0c8a5e8f75671ed2';
    const companyName = '深圳瑞贤金嘉网络科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateLegalPerson, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人持股比例低】', async () => {
    // 测试数据
    const companyId = '8f5e14fbeb8e0e2163bfae6f5151751b';
    const companyName = '中惠云电子商务廊坊市有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.legalRepresentHoldingRatio,
        fieldValue: [5],
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyFlag,
        fieldValue: ['FNC_JR', 'FNC_BX', 'FNC_ZJ'],
        options: [
          { label: '金融机构', value: 'FNC_JR' },
          { label: '保险机构', value: 'FNC_BX' },
          { label: '保险中介机构', value: 'FNC_ZJ' },
        ],
        accessScope: 4,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyIsBranch,
        fieldValue: [1],
        options: [{ label: '是分支机构', value: 1 }],
        accessScope: 4,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyEconType,
        fieldValue: ['*********', '001003', '001004', '001005', '001011', '001016', '001015', '001009', '001006', '001007'],
        options: [
          { label: '国有企业', value: '*********' },
          { label: '机关单位', value: '001003' },
          { label: '事业单位', value: '001004' },
          { label: '社会组织', value: '001005' },
          { label: '律师事务所', value: '001011' },
          { label: '学校', value: '001016' },
          { label: '医疗机构', value: '001015' },
          { label: '个体工商户', value: '001009' },
          { label: '个人独资企业', value: '001006' },
          { label: '合伙企业', value: '001007' },
        ],
        accessScope: 4,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法定代表人控制企业集中注册且均无实缴资本】', async () => {
    // 测试数据
    const companyId = 'a4460089d753148ebe6cf01702d799d2';
    const companyName = '福建娱加电子商务有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6709, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'startDate', order: 'DESC', fieldSnapshot: 'startDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      companyName,
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【同实际控制人企业众多增加不确定风险】', async () => {
    // 测试数据
    const companyId = '046d542571d5ea50373c42bc4fff1b7e';
    const companyName = '北京中合启元商贸有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk2310, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'percentTotal', order: 'DESC', fieldSnapshot: 'percentTotalNumber' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      companyName,
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【同法定代表人企业众多且地区分散】', async () => {
    // 测试数据
    const companyId = '9f936545570f423dde0a0802c3bf28b1';
    const companyName = '中欧睿意企业管理有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk2210, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [
          { field: 'stockPercent', order: 'DESC', fieldSnapshot: 'stockPercentNumber' },
          { field: 'regCap', order: 'DESC', fieldSnapshot: 'regCapNumber' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【受益所有人控制企业众多】', async () => {
    // 测试数据
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'startDate', order: 'DESC', fieldSnapshot: 'startDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实际控制人变更】-近期实际控制人变更', async () => {
    // 测试数据
    const companyId = '0ce5914df59d1e45deb5955b4af02935';
    const companyName = '广州荣陌科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdatePerson, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await riskChangeService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //近期实际控制人变更覆盖
  it.skip('【实际控制人变更】-近期实际控制人频繁变更', async () => {
    // 测试数据
    const companyId = '0ce5914df59d1e45deb5955b4af02935';
    const companyName = '广州荣陌科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdatePerson, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await riskChangeService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【联系方式或注册地址重复】-同联系方式企业', async () => {
    // 测试数据
    const companyId = '9cce0780ab7644008b73bc2120479d31';
    const companyName = '小米科技有限责任公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: ['HasPhone'],
        accessScope: 1,
        options: [{ label: '相同电话', value: NebulaRelatedEdgeEnums.HasPhone }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['10', '20'],
        accessScope: 2,
        options: [
          { name: '在业', value: '10' },
          { name: '存续', value: '20' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [20],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【联系方式或注册地址重复】-同注册地址企业', async () => {
    // 测试数据
    const companyId = 'de60dfcb5650e86505724f0d598d89d7';
    const companyName = '江西飞泰林产有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: ['HasAddress'],
        accessScope: 1,
        options: [{ label: '相同地址', value: NebulaRelatedEdgeEnums.HasAddress }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['10', '20'],
        accessScope: 2,
        options: [
          { name: '在业', value: '10' },
          { name: '存续', value: '20' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [20],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【大股东变更】-工商基本信息', async () => {
    // 测试数据
    const companyId = '9726d92752c802149c057b223ff9429c';
    const companyName = '德清恒海新能源有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【大股东变更】-近期大股东变更', async () => {
    // 测试数据
    const companyId = '9726d92752c802149c057b223ff9429c';
    const companyName = '德清恒海新能源有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateHolder, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await riskChangeService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //近期大股东变更覆盖
  it.skip('【大股东变更】-近期大股东频繁变更', async () => {
    // 测试数据
    const companyId = '9726d92752c802149c057b223ff9429c';
    const companyName = '德清恒海新能源有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateHolder, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await riskChangeService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await riskChangeService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【异地经营】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        accessScope: 1,
        fieldValue: ['2410'],
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权结构复杂】', async () => {
    // 测试数据
    const companyId = '003558f7ac3eae7b25f1394bc50042b5';
    const companyName = '深圳多元互联网科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        fieldValue: ['6801'],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【控制权分散】', async () => {
    // 测试数据
    const companyId = '000168087f4500ef10d633c0768b54ea';
    const companyName = '上海恋滟实业有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6803, []);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【所有权与经营权分离】', async () => {
    // 测试数据
    const companyId = '11e0fa056139464ed34c05e5fd6174f4';
    const companyName = '上海伟蓉清洗保洁服务有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6802, []);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权结构疑似隐藏控制方】', async () => {
    // 测试数据
    const companyId = 'edd75fab40249a5f95e07edcaadc9e7e';
    const companyName = '长沙通嘉企业管理咨询合伙企业（有限合伙）';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        accessScope: 1,
        fieldValue: ['6809'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【采用托管、代办、秘书公司注册】', async () => {
    // 测试数据
    const companyId = 'd4e973944b0b8bef524cafd675d77b67';
    const companyName = '华能工融二号（天津）股权投资基金合伙企业（有限合伙）';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        fieldValue: ['6504'],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【采用自主申报、住所申报方式注册】', async () => {
    // 测试数据
    const companyId = '3395ad1cb98840b88eb823d55cf4e4e6';
    const companyName = '碧纯食品饮料（浙江）有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk, [
      {
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        options: [],
        fieldValue: ['6505'],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实际控制人无法识别或穿透边界以外】', async () => {
    // 测试数据
    const companyId = '162ba9663a2a8be5fd50d0c1062bf60e';
    const companyName = '德江县先进核桃种植专业合作社';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6615, []);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实际控制人控制企业集中注册且均无实缴资本】', async () => {
    // 测试数据
    const companyId = 'd6e93f02539eaeffcadef4a484e4873b';
    const companyName = '广州润工网络科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6609, []);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实际控制人控制企业涉及高风险行业】', async () => {
    // 测试数据
    const companyId = '089623adb65a9602944079a6eeeddbcb';
    const companyName = '上海复星寰宇国际贸易有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6610, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [
          {
            field: 'startDate',
            order: 'DESC',
            fieldSnapshot: 'startDate',
          },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实际控制人控制企业位于边境贸易区】', async () => {
    // 测试数据
    const companyId = '4149d4ae6dea127fc695a33c2567d224';
    const companyName = '吉林省程阳工贸有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6611, []);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实际控制人控制企业边境贸易区占比较高】', async () => {
    // 测试数据
    const companyId = 'ad577da8e3b13403a895cddf0d04d37b';
    const companyName = '绥芬河市晟宏经贸有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6612, [
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [
          {
            field: 'startDate',
            order: 'DESC',
            fieldSnapshot: 'startDate',
          },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【经营范围变更】-近期经营范围变更', async () => {
    // 测试数据
    const companyId = 'd590a9db3a67516151146c2f44414cf5';
    const companyName = '内蒙古兴明机械租赁有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateScope, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //近期经营范围变更覆盖
  it.skip('【经营范围变更】-近期经营范围频繁变更', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateScope, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【注册地址变更】-近期注册地址变更', async () => {
    // 测试数据
    const companyId = 'd590a9db3a67516151146c2f44414cf5';
    const companyName = '内蒙古兴明机械租赁有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateAddress, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //近期注册地址变更覆盖
  it.skip('【注册地址变更】-近期注册地址频繁变更', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateAddress, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【企业名称变更】-近期企业名称变更', async () => {
    // 测试数据
    const companyId = 'd590a9db3a67516151146c2f44414cf5';
    const companyName = '内蒙古兴明机械租赁有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateName, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //近期企业名称变更覆盖
  it.skip('【企业名称变更】-近期企业名称频繁变更', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateName, [
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [2],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        accessScope: 1,
        fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【注册资本降幅过大】', async () => {
    // 测试数据
    const companyId = 'd6c66b23e6edbdba9e550f0316052b78';
    const companyName = '中建投易筑（沈阳）市政工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk6907, []);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【关联方企业经营异常】-关联方企业经营异常', async () => {
    // 测试数据
    const companyId = 'd6c66b23e6edbdba9e550f0316052b78';
    const companyName = '中建投易筑（沈阳）市政工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAnomalies, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [
          'PrincipalControl',
          'LegalRepresentativeControl',
          'ActualControllerControl',
          'BeneficiaryControl',
          'MotherCompanyMajorityShareholder',
          'MotherCompanyControl',
          'MajorityInvestment',
        ],
        accessScope: 1,
        options: [],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [{ label: '经营异常', value: 'Exception' }],
        accessScope: 2,
        fieldValue: ['Exception'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【关联方企业经营异常】-关联方企业经营异常（历史）', async () => {
    // 测试数据
    const companyId = 'd6c66b23e6edbdba9e550f0316052b78';
    const companyName = '中建投易筑（沈阳）市政工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAnomalies, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [
          'PrincipalControl',
          'LegalRepresentativeControl',
          'ActualControllerControl',
          'BeneficiaryControl',
          'MotherCompanyMajorityShareholder',
          'MotherCompanyControl',
          'MajorityInvestment',
        ],
        accessScope: 1,
        options: [],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [{ label: '经营异常', value: 'Exception' }],
        accessScope: 2,
        fieldValue: ['Exception'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【关联方企业违法事项】-关联方企业被吊销', async () => {
    // 测试数据
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [
          'PrincipalControl',
          'LegalRepresentativeControl',
          'ActualControllerControl',
          'BeneficiaryControl',
          'MotherCompanyMajorityShareholder',
          'MotherCompanyControl',
          'MajorityInvestment',
        ],
        accessScope: 1,
        options: [],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        options: [{ name: '吊销', value: '90' }],
        accessScope: 2,
        fieldValue: ['90'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【关联方企业违法事项】-关联方企业存在违法事项', async () => {
    // 测试数据
    const companyId = 'd6c66b23e6edbdba9e550f0316052b78';
    const companyName = '中建投易筑（沈阳）市政工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SeriousViolation, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [
          'PrincipalControl',
          'LegalRepresentativeControl',
          'ActualControllerControl',
          'BeneficiaryControl',
          'MotherCompanyMajorityShareholder',
          'MotherCompanyControl',
          'MajorityInvestment',
        ],
        accessScope: 1,
        options: [],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [
          { label: '严重违法', value: 'SeriousViolation' },
          { label: '失信被执行人', value: 'BadCreditExecuted' },
          { label: '税收违法', value: 'TaxIllegal' },
        ],
        accessScope: 2,
        fieldValue: ['SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [-1],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【关联方企业违法事项】-关联方企业存在违法事项(历史)', async () => {
    // 测试数据
    const companyId = 'd6c66b23e6edbdba9e550f0316052b78';
    const companyName = '中建投易筑（沈阳）市政工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SeriousViolation, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [
          'PrincipalControl',
          'LegalRepresentativeControl',
          'ActualControllerControl',
          'BeneficiaryControl',
          'MotherCompanyMajorityShareholder',
          'MotherCompanyControl',
          'MajorityInvestment',
        ],
        accessScope: 1,
        options: [],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [
          { label: '严重违法', value: 'SeriousViolation' },
          { label: '失信被执行人', value: 'BadCreditExecuted' },
          { label: '税收违法', value: 'TaxIllegal' },
        ],
        accessScope: 2,
        fieldValue: ['SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [3],
        options: [-1, 1, 3, 5],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [0],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【关联方企业刑事案件】', async () => {
    // 测试数据
    const companyId = 'e81536f0a9462b837b77ed84a2a463f7';
    const companyName = '南京广告影像公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MoneyLaundering, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [
          'PrincipalControl',
          'LegalRepresentativeControl',
          'ActualControllerControl',
          'BeneficiaryControl',
          'MotherCompanyMajorityShareholder',
          'MotherCompanyControl',
          'MajorityInvestment',
        ],
        accessScope: 1,
        options: [],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [{ label: '裁判文书', value: 'Judgement' }],
        accessScope: 2,
        fieldValue: ['Judgement'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      // 案件身份 被告
      {
        fieldKey: DimensionFieldKeyEnums.judgementRole,
        fieldValue: [1],
        accessScope: 2,
        options: [
          { label: '原告', value: 0 },
          { label: '被告', value: 1 },
          { label: '第三人', value: 2 },
          { label: '其他', value: 3 },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        options: [{ label: '刑事案件', value: 'xs' }],
        accessScope: 2,
        fieldValue: ['xs'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [-1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【关联方企业集中注册且均无实缴资本】', async () => {
    // 测试数据
    const companyId = 'ecf3750fd98afb41bdaabd4033970f76';
    const companyName = '深圳市尼频运输有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk7099, []);

    // 调用 analyze 方法
    const result = await proService.analyze(companyId, [dimension], { companyName, keyNo: companyId });
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      companyName,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【同联系方式企业存在异常】', async () => {
    // 测试数据
    const companyId = '99b34826777e13180b5a1da41a42fd2f';
    const companyName = '四川省铭贵能源投资有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        options: [{ label: '相同电话', value: 'HasPhone' }],
        accessScope: 1,
        fieldValue: ['HasPhone'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [
          { label: '裁判文书', value: 'Judgement' },
          { label: '经营异常', value: 'Exception' },
          { label: '严重违法', value: 'SeriousViolation' },
          { label: '失信被执行人', value: 'BadCreditExecuted' },
          { label: '税收违法', value: 'TaxIllegal' },
        ],
        accessScope: 2,
        fieldValue: ['Judgement', 'Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        options: [{ label: '刑事案件', value: 'xs' }],
        accessScope: 2,
        fieldValue: ['xs'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judgementRole,
        fieldValue: [1],
        accessScope: 2,
        options: [
          { label: '原告', value: 0 },
          { label: '被告', value: 1 },
          { label: '第三人', value: 2 },
          { label: '其他', value: 3 },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 2, 3, 4, 5], // 前端有近5年的选项
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【同注册地址企业存在异常】', async () => {
    // 测试数据
    const companyId = '2400779f25667882f16d6cf638468bd7';
    const companyName = '广东捷辰天丰精装修工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        options: [{ label: '相同电话', value: 'HasPhone' }],
        accessScope: 1,
        fieldValue: ['HasPhone'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        options: [
          { label: '裁判文书', value: 'Judgement' },
          { label: '经营异常', value: 'Exception' },
          { label: '严重违法', value: 'SeriousViolation' },
          { label: '失信被执行人', value: 'BadCreditExecuted' },
          { label: '税收违法', value: 'TaxIllegal' },
        ],
        accessScope: 2,
        fieldValue: ['Judgement', 'Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseType,
        options: [{ label: '刑事案件', value: 'xs' }],
        accessScope: 2,
        fieldValue: ['xs'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judgementRole,
        fieldValue: [1],
        accessScope: 2,
        options: [
          { label: '原告', value: 0 },
          { label: '被告', value: 1 },
          { label: '第三人', value: 2 },
          { label: '其他', value: 3 },
        ],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      // 案由 不是 交通肇事罪
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        accessScope: 2,
        fieldValue: [['A0248'], ['A0249']],
        options: [
          { label: '交通肇事罪', value: ['A0248'] },
          { label: '危险驾驶罪', value: ['A0249'] },
        ],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [-1, 1, 2, 3, 4, 5], // 前端有近5年的选项
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await nebulaGraphService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【政府采购严重违法失信行为记录名单】', async () => {
    // 测试数据
    const companyId = '5434bc470390801606fc2ea43500bdb9';
    const companyName = '福建闽龙保安服务有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['GovernmentPurchaseIllegal'],
        options: [{ value: 'GovernmentPurchaseIllegal', label: '政府采购严重违法失信行为记录名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【国央企采购黑名单】', async () => {
    // 测试数据
    const companyId = '766f04948eaedb5aeedbe2775266ed82';
    const companyName = '广东美的暖通设备有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['GovProcurementIllegal'],
        options: [{ value: 'GovProcurementIllegal', label: '国央企采购黑名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【安全生产领域失信生产经营单位】', async () => {
    // 测试数据
    const companyId = 'bbe9b782c272ea4cdc216d52b0548484';
    const companyName = '洛阳捷运工业设备安装有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['SafetyProductionEnterprise'],
        options: [{ value: 'SafetyProductionEnterprise', label: '安全生产领域失信生产经营单位' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【环保失信黑名单】', async () => {
    // 测试数据
    const companyId = 'f3b7fb79bf0ec64587551b0c84410479';
    const companyName = '上海威正环境技术有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['EnvironmentalProtection'],
        options: [{ value: 'EnvironmentalProtection', label: '环保失信黑名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【海关失信企业名单】', async () => {
    // 测试数据
    const companyId = 'fb8a1cdeb4fab417898af584c85c1b4d';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['CustomsList'],
        options: [{ value: 'CustomsList', label: '海关失信企业名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【违法失信上市公司】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['ListedCompanyIllegal'],
        options: [{ value: 'ListedCompanyIllegal', label: '违法失信上市公司' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【劳动保障违法】', async () => {
    // 测试数据
    const companyId = '17d10e3432c3b97ee4b5b8e0608bf989';
    const companyName = '淮安市新起航教育科技有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['LaborGuarantee'],
        options: [{ value: 'LaborGuarantee', label: '劳动保障违法' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【重点行业领域监管黑名单】', async () => {
    // 测试数据
    const companyId = '0c5a42026e7c4d9fce1cc3c9e8a62482';
    const companyName = '陕西万葫堂中医药有限责任公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['SupervisionOfKeyIndustry'],
        options: [{ value: 'SupervisionOfKeyIndustry', label: '重点行业领域监管黑名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【知识产权（专利）领域严重失信联合戒对象名单】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['IntellectualPropertyIllegal'],
        options: [
          {
            value: 'IntellectualPropertyIllegal',
            label: '知识产权（专利）领域严重失信联合戒对象名单',
          },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【发改委黑名单】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['FgwBlackList'],
        options: [{ value: 'FgwBlackList', label: '发改委黑名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【出口管制合规风险企业清单】', async () => {
    // 测试数据
    const companyId = '8051867d0a3d25e91879beed7c7eafba';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.sanctionListCodes,
        accessScope: 2,
        fieldValue: ['ForeignExportControlList'],
        options: [{ value: 'ForeignExportControlList', label: '出口管制合规风险企业清单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【军队采购失信名单】', async () => {
    // 测试数据
    const companyId = '61746a52191d94c7e58ae75aeadbacfc';
    const companyName = '成都锦江电子系统工程有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitOuterBlackList, [
      {
        fieldKey: DimensionFieldKeyEnums.blackType,
        accessScope: 2,
        fieldValue: ['ArmyProcurementIllegal'],
        options: [{ value: 'ArmyProcurementIllegal', label: '军队采购失信名单' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await outerBlacklistService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await outerBlacklistService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【来自高风险I类国家或地区】', async () => {
    // 测试数据
    const companyId = 'ff3d5fa379cc82c57cb47f289c2670f7';
    const companyName = '义乌市科法贸易有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk1410, []);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【来自高风险II类、III类国家或地区】', async () => {
    // 测试数据
    const companyId = '3389403d1211f251f5389009cdc12eb7';
    const companyName = '义乌市姗科贸易有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk1411, []);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await proService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【央企】', async () => {
    // 测试数据
    const companyId = '837e8c3db3440424d29a579e27bd4b95';
    const companyName = '中国银行股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyEconType,
        accessScope: 2,
        options: [
          { label: '央企', value: '*********001' },
          { label: '央企子公司', value: '*********002' },
        ],
        fieldValue: ['*********001', '*********002'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【国企】', async () => {
    // 测试数据
    const companyId = '7f0763bf3ee9bcc174898b0f5f85c602';
    const companyName = '浙江省农村发展集团有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyEconType,
        accessScope: 2,
        options: [
          { label: '省管国企', value: '*********003' },
          { label: '市管国企', value: '*********004' },
          { label: '国有全资企业', value: '*********005' },
          { label: '国有独资企业', value: '*********006' },
          { label: '国有控股企业', value: '*********007' },
          { label: '国有实际控制企业', value: '*********008' },
        ],
        fieldValue: ['*********003', '*********004', '*********005', '*********006', '*********007', '*********008'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实缴资本】-工商基本信息-1亿-5亿', async () => {
    // 测试数据
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
        accessScope: 2,
        options: [{ label: '1亿-5亿', value: [10000, 50000] }],
        fieldValue: [[10000, 50000]],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【净利润】-工商基本信息-100亿以上', async () => {
    // 测试数据
    const companyId = 'ab603b8e94724170604c95f2bd3bcc9d';
    const companyName = '中国石油化工股份有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.netProfit,
        accessScope: 2,
        options: [{ label: '100亿以上', value: [1000000] }],
        fieldValue: [[1000000]],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【重点金融机构】', async () => {
    // 测试数据
    const companyId = '7d2ff2d16325bdc9e1a4e93b6f2f3132';
    const companyName = '葫芦岛市泳乐琪制衣有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.FinancialInstitution, [
      {
        fieldKey: DimensionFieldKeyEnums.financialInstitutionType,
        accessScope: 2,
        options: [
          { value: '1', label: '银行' },
          { value: '3', label: '保险机构' },
          { value: '4', label: '证券公司' },
          { value: '6', label: '基金公司' },
          { value: '2', label: '信托公司' },
          { value: '5', label: '期货公司' },
          { value: '9', label: '银行理财子公司' },
          { value: '14', label: '金融资管公司' },
        ],
        fieldValue: ['1', '3', '4', '6', '2', '5', '9', '14'],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);

    // 调用 analyze 方法
    const result = await enterpriseService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await enterpriseService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【融资上市】', async () => {
    // 测试数据
    const companyId = '3f6424692f41326a95c80aa3e331e06e';
    const companyName = '绿城房地产集团有限公司';

    // 构建维度策略
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyListed,
        accessScope: 2,
        options: [{ label: '上市公司(非ST/*ST)', value: 3 }],
        fieldValue: [3],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);

    // 调用 analyze 方法
    const result = await companyService.analyze(companyId, [dimension]);
    expect(result[0].totalHits).toBeGreaterThanOrEqual(1);

    // 获取维度详情
    const detail = await companyService.getDimensionDetail(dimension, {
      keyNo: companyId,
      pageSize: 10,
      pageIndex: 1,
    });
    expect(detail).not.toBeNull();
    expect(detail.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【疑似停业歇业停产或被吊销证照】- 企业经营状态异常', async () => {
    const companyId = '2b7ea04a4990f452ad59168371b28efc';
    const companyName = '海南央吉石油及制品批发有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal5);

    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result?.status).toEqual('OK');
  });

  it('【税务催缴】- 企业存在税务催缴记录', async () => {
    const companyId = '3b08cebfc6d596a6bee121b35a30d5c0';
    const companyName = '上海电器城市场经营管理有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxCallNoticeV2);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【劳动纠纷】- 企业存在劳动合同纠纷', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.LaborContractDispute);

    const detail = await caseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await caseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【公司及主要人员涉刑事犯罪】- 近3年刑事犯罪记录', async () => {
    const companyId = '5bab9c07ca9fc40355becad664d502c2';
    const companyName = '麻城市华祥再生资源循环利用有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyOrMainMembersCriminalOffence);

    const detail = await caseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await caseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【产品召回】- 企业存在产品召回问题', async () => {
    const companyId = '8f463e3e6c2d4a3b44e3f61641e331c8';
    const companyName = '贝克曼库尔特商贸（中国）有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProductQualityProblem1);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【药品抽查检验不合格】- 企业药品抽查不合格记录', async () => {
    const companyId = '74d5755499819fde97110df41f154967';
    const companyName = '江西和硕药业有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProductQualityProblem7);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【食品安全检查不合格】- 企业食品安全抽查不合格记录', async () => {
    const companyId = '7a32505cb08af10632c993ece502f9da';
    const companyName = '安徽省小岗盼盼食品有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProductQualityProblem9);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【票据违约】- 企业存在票据违约记录', async () => {
    const companyId = '8e66ac5dc8883c518c0c4046ab7a9ae1';
    const companyName = '中经国开建工（山东）有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BillDefaults);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【终本案件】- 企业涉及终本案件', async () => {
    const companyId = '56cfb29252cc9769fff84cfec0866c2e';
    const companyName = '奥途（北京）二手车鉴定评估有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EndExecutionCase);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【公司及主要人员涉刑事犯罪(历史)】- 3年以上刑事犯罪记录', async () => {
    const companyId = 'ae6aa0b6249b0b7a766e9e75af4971b6';
    const companyName = '江西省乾庄典当有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory);

    const detail = await caseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await caseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【近3年涉贪污受贿裁判相关提及方】- 涉贪污受贿案件被提及', async () => {
    const companyId = 'aa15799d5eceefc46b199be5581480c3';
    const companyName = '潮安县庵埠长丰食品厂';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyOrMainMembersCriminalInvolve);

    // const detail = await judgementService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 涉贪污受贿裁判相关提及方(历史)
  it('【涉贪污受贿裁判相关提及方(历史)】- 3年以上涉贪污受贿案件被提及', async () => {
    const companyId = '55cef6814dfd9e340a8a35d7841f7b1c';
    const companyName = '中国储备粮管理集团有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory);

    // const detail = await judgementService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 税务催缴(旧版)
  it.skip('【税务催缴(旧版)】- 企业存在旧版税务催缴记录', async () => {
    const companyId = 'ee17584b5c4ef35901dccc77039d335d';
    const companyName = '广西宽度企业管理咨询有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxCallNotice);

    const detail = await taxService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await taxService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 税务催报
  it('【税务催报】- 企业存在税务催报记录', async () => {
    const companyId = '8d378a926c8fe1649760b1d1826b92eb';
    const companyName = '广州元来酒店管理有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxReminder);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 债券违约
  it('【债券违约】- 企业存在债券违约记录', async () => {
    const companyId = '3ca1e9af5088420d08c4b0b01c863617';
    const companyName = '华晨汽车集团控股有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BondDefaults);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 动产抵押
  it('【动产抵押】- 企业存在动产抵押记录', async () => {
    const companyId = '40f2003a487285ea1bd41a89d67adcea';
    const companyName = '中电投融和融资租赁有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ChattelMortgage);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 土地抵押
  it.skip('【土地抵押】- 企业存在土地抵押记录', async () => {
    const companyId = 'efc473bce29691a432df959deb9a6931';
    const companyName = '中国建设银行股份有限公司新化支行';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.LandMortgage);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 司法拍卖
  it('【司法拍卖】- 企业资产被司法拍卖', async () => {
    const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    const companyName = '乐视网信息技术（北京）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.JudicialAuction);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 买卖合同纠纷
  it('【买卖合同纠纷】- 企业涉及买卖合同纠纷', async () => {
    const companyId = 'a57e36800d26e25d0b31676f9d440db8';
    const companyName = '青岛昊河水泥制品有限责任公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SalesContractDispute);

    // const detail = await judgementService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 重大争议
  it.skip('【重大争议】- 企业涉及重大争议', async () => {
    const companyId = 'ad5fad98090405e94123325301ff053f';
    const companyName = '广西建工集团建筑工程总承包有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MajorDispute);

    const detail = await judgementService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await judgementService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 行政处罚3年前
  it.skip('【行政处罚3年前】- 企业3年前行政处罚记录', async () => {
    const companyId = '0f419f0da751ddfb6ebd02770aabdebd';
    const companyName = '苍南县康保龄酒业有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AdministrativePenalties3);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【被列为非正常户】- 企业被列为非正常户', async () => {
    const companyId = 'e4f619bf95c1c6a648b34b0955af8465';
    const companyName = '福建丰益进出口贸易有限公司';
    //废弃
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BusinessAbnormal4);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【无实缴资本】- 企业无实缴资本', async () => {
    const companyId = '0001f58758c41029f03e45fc9c4effd4';
    const companyName = '上海睿钻贸易有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.NoCapital);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【成立时长小于24个月】- 企业成立时间较短', async () => {
    const companyId = 'e464355ad73892937dc196e947e942e6';
    const companyName = '浙江耀厦控股集团有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EstablishedTime);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【注册资本小于100万】- 企业注册资本过低', async () => {
    const companyId = '69a928cc07c8e7d89a1e325bf3f4aad6';
    const companyName = '北京国俱科技有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.LowCapital);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【实缴资本异常】- 企业实缴资本异常', async () => {
    const companyId = 'cd35d087575b9b616e59ed4d072661fd';
    const companyName = '上海盛唐置业有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RealCapitalException);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【无中标项目】- 企业无中标项目记录', async () => {
    const companyId = '69a928cc07c8e7d89a1e325bf3f4aad6';
    const companyName = '北京国俱科技有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.NoTender);

    const detail = await tenderService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await tenderService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 主体变更类测试
  it.skip('【近期变更受益所有人】- 企业近期变更受益所有人', async () => {
    const companyId = '90f9f13d2c9a1f6b0c8a5e8f75671ed2';
    const companyName = '深圳瑞贤金嘉网络科技有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateBeneficiary);

    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【减资公告】- 企业发布减资公告', async () => {
    const companyId = '5434bc470390801606fc2ea43500bdb9';
    const companyName = '福建闽龙保安服务有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CapitalReduction);

    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【假冒化妆品】- 企业生产假冒化妆品记录', async () => {
    const companyId = '2f8894f81306c8d9c42bd88f04085eca';
    const companyName = '广东优亿美化妆品有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProductQualityProblem8);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // 税务风险类测试
  it('【欠税公告】- 企业存在欠税公告', async () => {
    const companyId = '3d0c5112a30ee9177e850c809c18bef4';
    const companyName = '绿地控股集团有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.TaxArrearsNotice);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【涉诈高风险名单】- 企业在涉诈高风险名单中', async () => {
    const companyId = 'd6435e7b6d3bc097ee1e86a297530c1e';
    const companyName = '大连强志永发装修工程有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.FraudList);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await companyService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【查查信用分】', async () => {
    const companyId = '8b009d71dfb2796cceba82988d244049';
    const companyName = '上海牛推信息科技有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QCCCreditRate);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【出口管制合规风险企业清单】- 企业在出口管制合规风险企业清单中', async () => {
    const companyId = 'd6435e7b6d3bc097ee1e86a297530c1e';
    const companyName = '大连强志永发装修工程有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.OvsSanction);

    const detail = await ovsSanctionsService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await ovsSanctionsService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【联系方式或注册地址重复】', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.QfkRisk2010);

    const detail = await companyService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await proService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【关联方开庭公告】', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedAnnouncement);

    const detail = await nebulaGraphService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await nebulaGraphService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【外部关联风险】- 企业外部关联风险', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ExternalRelatedRisk);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【股权质押】- 企业股权质押情况', async () => {
    const companyId = 'd0ae8725a1aa17ddeb9ae26f6f451451';
    const companyName = '河南酒便利商业股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.StockPledge);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【财务健康】- 企业财务健康状况', async () => {
    const companyId = 'cb409311e6aea8b9788552af9f17b813';
    const companyName = '正泰安能数字能源（浙江）股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.FinancialHealth);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【清算信息】- 企业清算信息', async () => {
    const companyId = '50c47d235a3290461599552ee9ade2b6';
    const companyName = '合肥钢铁集团有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Liquidation);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //roverService已废弃
  it.skip('【疑似利益冲突】- 企业疑似利益冲突情况', async () => {
    // const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    // const companyName = '农夫山泉股份有限公司';
    //
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.SuspectedInterestConflict);
    //
    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //roverService已废弃
  it.skip('【潜在利益冲突-股权/任职关联】', async () => {
    // const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    // const companyName = '农夫山泉股份有限公司';
    //
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.StaffWorkingOutsideForeignInvestment);
    //
    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【主要人员限制高消费】- 企业主要人员限制高消费情况', async () => {
    const companyId = '2fc20223dd281981cdc486981d4ff1c2';
    const companyName = '上海隆琪机电设备有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【主要人员失信】- 企业主要人员失信情况', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainMembersPersonCreditCurrent);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditAPIService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【产品未准入境】- 企业产品未准入境情况', async () => {
    const companyId = '6583579d4d007d7daab586c94d592e93';
    const companyName = '优合集团有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProductQualityProblem6);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditAPIService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【产品抽查不合格】- 企业产品抽查不合格情况', async () => {
    const companyId = '126b6fc95a38c8ac213caa0659978e15';
    const companyName = '昆明纳鑫服装有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ProductQualityProblem2);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【监管处罚】', async () => {
    const companyId = '39e3cbd13bcf3b526d1a5d204ace93ec';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RegulateFinance);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【资质认证】- 企业资质认证情况', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.Certification);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【无资质认证】- 企业无资质认证情况', async () => {
    const companyId = 'ea79831f2cbe8224f6fcab013a144175';
    const companyName = '上海正辅云企业发展有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.NoCertification);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【无质量认证】- 企业无质量认证情况', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.NoQualityCertification);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //roverService已废弃
  it.skip('【合作方黑名单调查】- 企业合作方黑名单调查', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BlacklistPartnerInvestigation);

    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //roverService已废弃
  it.skip('【劳动关系】- 企业劳动关系情况', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.EmploymentRelationship);

    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //roverService已废弃
  it.skip('【命中内部黑名单】- 企业命中内部黑名单情况', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.HitInnerBlackList);

    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【担保信息】- 企业担保信息', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.GuaranteeInfo);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it.skip('【担保风险】- 企业担保风险情况', async () => {
    const companyId = '10dd4d87b447cc28c3b88d6e7ae24424';
    const companyName = '华晨汽车集团控股有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.GuaranteeRisk);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【行政处罚（涉及商业贿赂、垄断行为或政府采购活动违法行为）】', async () => {
    const companyId = 'caf9fe6dce4edc64a1158591095e0b03';
    const companyName = '宁海晓渝汽车维修服务部';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.AdministrativePenalties2);

    const detail = await creditESService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await creditESService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【合同违约】- 企业合同违约情况', async () => {
    const companyId = '02eca136b16504350746365fab632977';
    const companyName = '中安金控资产管理有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ContractBreach);

    const detail = await enterpriseService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await enterpriseService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  //roverService已废弃
  it.skip('【第三方列表交叉重叠疑似关系】', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CustomerSuspectedRelation);

    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
  //roverService已废弃
  it.skip('【黑名单列表交叉重叠疑似关系】', async () => {
    const companyId = 'f8954d7fa9913cbb9c8ca4e1c01a7132';
    const companyName = '农夫山泉股份有限公司';

    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.BlacklistSuspectedRelation);

    // const detail = await creditESService.analyze(companyId, [dimension]);
    // expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    // const result = await creditESService.getDimensionDetail(
    //   dimension,
    //   Object.assign(
    //     new HitDetailsBaseQueryParams(),
    //     {
    //       keyNo: companyId,
    //       pageIndex: 1,
    //       pageSize: 10,
    //     },
    //     { keyNo: companyId, companyName },
    //   ),
    // );
    // expect(result).not.toBeNull();
    // expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });
});
