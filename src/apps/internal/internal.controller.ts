import { Body, Controller, Get, Param, ParseIntPipe, Post, Query, Req, Request, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ApiGuardInternal } from '../../libs/guards/api.guard.internal';
import { RiskModelInitService } from '../risk_model/init_mode/risk_model.init.service';
import { PlatformUser } from '../../client';
import { RiskModelTypeEnums } from '../../libs/enums/RiskModelTypeEnums';
import { ModelInitMonitorService } from '../risk_model/init_mode/model.init.monitor.service';
import { GetJwtTokenRequest } from '../../libs/model/internal/InternalRequest';
import { ExportConditionRequest } from '../../libs/model/batch/po/message/BatchExportMessagePO';
import { DiligencePDFService } from '../diligence/diligence.pdf.service';
import { MonitorJobService } from '../monitor/monitor.job.service';
import { PushJobService } from '../push/push.job.service';
import { ModelInitMonitorSellerService } from '../risk_model/init_mode/model.init.monitor.seller.service';
import { InternalService } from './internal.service';
import { RiskModelInitBocLuService } from '../risk_model/init_mode/risk_model.init.boc.lu.service';
import { ModelInitICBCSZMonitorService } from '../risk_model/init_mode/model.init.monitor.icbc.sz.service';
import { RiskModelSTIHealthService } from '../risk_model/init_mode/risk_model.init.STI.Indicators.service';
import { RiskModelICBCSZService } from "../risk_model/init_mode/risk_model.init.icbc.sz.service";

@Controller('internal')
@UseGuards(ApiGuardInternal)
export class InternalController {
  constructor(
    private readonly initModelService: RiskModelInitService,
    private readonly modelInitMonitorService: ModelInitMonitorService,
    private readonly modelInitMonitorSellerService: ModelInitMonitorSellerService,
    private readonly diligencePDFService: DiligencePDFService,
    private readonly monitorJobService: MonitorJobService,
    private readonly pushJobService: PushJobService,
    private readonly internalService: InternalService,
    private readonly initModelBocLuService: RiskModelInitBocLuService,
    private readonly modelInitICBCSZMonitorService: ModelInitICBCSZMonitorService,
    private readonly riskModelSTIHealthService: RiskModelSTIHealthService,
    private readonly initModelICBCSZService: RiskModelICBCSZService,
  ) {}

  @Post('creat_htf_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiQuery({
    name: 'modelType',
    required: false,
    description: '模型类型， 1尽调模型 2： 监控模型 默认是1',
    type: Number,
  })
  @ApiTags('尽调模型创建')
  @ApiOperation({ description: '创建汇添富模型分发给指定租户' })
  async editRiskModel(@Req() req: any, @Query('name') name: string, @Query('toOrgId') toOrgId: number, @Query('modelType') modelType: RiskModelTypeEnums) {
    const currentUser: PlatformUser = req.user;
    return this.initModelService.createHTFModel(currentUser, name, toOrgId, modelType);
  }

  @Post('create_SIT_health_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiQuery({
    name: 'modelType',
    required: false,
    description: '模型类型， 1尽调模型 2： 监控模型 默认是1',
    type: Number,
  })
  @ApiTags('尽调模型创建')
  @ApiOperation({ description: '创建科创指标尽调模型分发给指定租户' })
  async createSTIHealthModel(
    @Req() req: any,
    @Query('name') name: string,
    @Query('toOrgId') toOrgId: number,
    @Query('modelType') modelType: RiskModelTypeEnums,
  ) {
    const currentUser: PlatformUser = req.user;
    return this.riskModelSTIHealthService.createSTIIndicatorsModel(currentUser, name, toOrgId, modelType);
  }

  @Post('creat_boc_lu_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiTags('尽调模型创建')
  @ApiOperation({ description: '创建中国银行山东省分行模型' })
  async editRiskModelForBocLu(@Req() req: any, @Query('name') name: string, @Query('toOrgId') toOrgId: number) {
    const currentUser: PlatformUser = req.user;
    return this.initModelBocLuService.createBOCLuModel(currentUser, name, toOrgId, 1);
  }

  @Post('creat_monitor_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiQuery({
    name: 'modelType',
    required: false,
    description: '模型类型， 1尽调模型 2： 监控模型 默认是2',
    type: Number,
  })
  @ApiTags('监控模型创建')
  @ApiOperation({ description: '创建监控模型分发给指定租户' })
  async createMonitorModel(@Req() req: any, @Query('name') name: string, @Query('toOrgId') toOrgId: number, @Query('modelType') modelType: RiskModelTypeEnums) {
    const currentUser: PlatformUser = req.user;
    return this.modelInitMonitorService.createMonitorModel(currentUser, name, toOrgId, modelType);
  }

  @Post('creat_seller_monitor_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiQuery({
    name: 'modelType',
    required: false,
    description: '模型类型 2：监控模型 默认是2',
    type: Number,
  })
  @ApiTags('监控模型创建')
  @ApiOperation({ description: '创建给销售的监控模型分发给指定租户' })
  async createSellerMonitorModel(
    @Req() req: any,
    @Query('name') name: string,
    @Query('toOrgId') toOrgId: number,
    @Query('modelType') modelType: RiskModelTypeEnums,
  ) {
    const currentUser: PlatformUser = req.user;
    return this.modelInitMonitorSellerService.createSellerMonitorModel(currentUser, name, toOrgId, modelType);
  }

  @Post('creat_icbc_monitor_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiQuery({
    name: 'modelType',
    required: false,
    description: '模型类型 2：监控模型 默认是2',
    type: Number,
  })
  @ApiTags('监控模型创建')
  @ApiOperation({ description: '创建sf监控模型分发给指定租户' })
  async createIcbcMonitorModel(
    @Req() req: any,
    @Query('name') name: string,
    @Query('toOrgId') toOrgId: number,
    @Query('modelType') modelType: RiskModelTypeEnums,
  ) {
    const currentUser: PlatformUser = req.user;
    return this.modelInitICBCSZMonitorService.createMonitorModel(currentUser, name, toOrgId, modelType);
  }

  @Post('creat_icbc_risk_model')
  @ApiQuery({ name: 'name', required: true, description: '模型名称', type: String })
  @ApiQuery({ name: 'toOrgId', required: true, description: '分发给的租户id', type: Number })
  @ApiQuery({
    name: 'modelType',
    required: false,
    description: '模型类型 1：尽调模型 默认是1',
    type: Number,
  })
  @ApiTags('尽调模型创建')
  @ApiOperation({ description: '创建sf贷前尽调模型分发给指定租户' })
  async createIcbcRiskModel(
    @Req() req: any,
    @Query('name') name: string,
    @Query('toOrgId') toOrgId: number,
    @Query('modelType') modelType: RiskModelTypeEnums,
  ) {
    const currentUser: PlatformUser = req.user;
    return this.initModelICBCSZService.createICBCSZModel(currentUser, name, toOrgId, modelType);
  }

  // @Get('clear_model_data')
  // @ApiOperation({ description: '清楚所有模型和尽调数据' })
  // async clearModelData(@Req() req: any) {
  //   const currentUser: PlatformUser = req.user;
  //   return this.initModelService.delModelData(currentUser);
  // }

  @Post('init_default_dimensionDefs')
  @ApiTags('模型相关接口')
  @ApiOperation({ description: '初始化默认维度定义' })
  async initDefaultDimensionDefs(@Req() req: any) {
    const currentUser: PlatformUser = req.user;
    return this.initModelService.initDefaultDimensionDefs(currentUser);
  }

  @Post('check_module_company_validate')
  @ApiTags('模型相关接口')
  @ApiOperation({ description: '验证模型的企业通过情况' })
  async checkModuleCompanyValidate(@Req() req: any, @Query('moduleId') modelId: number, @Query('modelType') modelType: RiskModelTypeEnums) {
    const currentUser: PlatformUser = req.user;
    return this.internalService.checkModuleCompanyValidate(modelId, modelType);
  }

  @Get('/manuallyModifyRelatedSettings/:modelId')
  @ApiTags('模型相关接口')
  @ApiOperation({ description: '修改件监控模型中的监控范围设置，直接修改已发布模型的数据，不用重新生成模型变更模型id' })
  @ApiQuery({ name: 'modelId', type: String, required: false, description: '需要修改的模型id' })
  @ApiQuery({
    name: 'newFieldValue',
    type: String,
    required: true,
    description:
      '新的关联方范围定义，逗号,分隔，可选值为:\n PrincipalControl(主要人员控制企业),\n LegalRepresentativeControl(法定代表人控制企业),\n ActualControllerControl(实际控制人控制企业), BeneficiaryControl(受益人控制企业),MotherCompanyMajorityShareholder(母公司), MotherCompanyControl(母公司控制企业), MajorityInvestment(子公司（对外投资), Branch(分支机构)',
  })
  async manuallyModifyRelatedSettings(@Query('secret') secret: string, @Param('modelId') modelId: string, @Query('newFieldValue') newFieldValue: string) {
    if (secret !== 'asdf') {
      return {
        code: 403,
        message: 'secret error',
      };
    }
    const fieldValue = newFieldValue.split(',');
    if (fieldValue.length < 0) {
      return {
        code: 400,
        message: 'newFieldValue 不能为空',
      };
    }
    return this.internalService.manuallyModifyRelatedSettings(+modelId, fieldValue);
  }

  @Post('getJwtToken')
  @ApiTags('其他内部接口')
  @ApiOperation({ description: '获取 jwToken' })
  async getJwtToken(@Req() req: any, @Body() reqBody: GetJwtTokenRequest) {
    const currentUser: PlatformUser = req.user;
    return this.internalService.getJwtToken(currentUser, reqBody);
  }

  @Get('/diligencePdf/:diligenceId')
  @ApiTags('其他内部接口')
  @ApiOperation({ description: '排查报告html' })
  async getDiligenceHtml(@Param('diligenceId', ParseIntPipe) diligenceId: number, @Request() req) {
    const currentUser: PlatformUser = req.user;
    const condition: ExportConditionRequest = { diligenceId, product: currentUser.currentProduct };
    return this.diligencePDFService.getDiligenceHtml(currentUser, condition);
  }

  @Get('/executeMonitorJob/:monitorGroupIds')
  @ApiTags('监控任务')
  @ApiOperation({ description: '给指定监控分组执行监控任务' })
  @ApiQuery({ name: 'interval', type: String, required: false, description: '本次监控任务查询数据的时间区间' })
  @ApiQuery({ name: 'intervalUnit', type: String, required: false, description: '时间区间的单位  day ', enum: ['year', 'month', 'day', 'hour'] })
  async doMonitorJob(
    @Param('monitorGroupIds') monitorGroupIds: string,
    @Query('interval') interval: string,
    @Query('secret') secret: string,
    @Query('intervalUnit') intervalUnit: 'year' | 'month' | 'day' | 'hour',
  ) {
    if (secret !== 'asdf') {
      return {
        code: 403,
        message: 'secret error',
      };
    }
    monitorGroupIds.split(',').forEach((monitorGroupId) => {
      this.monitorJobService.monitorGroupDynamicJob(+monitorGroupId, +interval, intervalUnit);
    });
    return monitorGroupIds.split.length;
  }

  @Get('/pushRuleRuleJob/:monitorGroupId')
  @ApiTags('监控任务')
  @ApiOperation({ description: '给指定监控分组执行动态推送' })
  async pushRuleRuleJob(@Param('monitorGroupId', ParseIntPipe) monitorGroupId: number) {
    await this.pushJobService.pushRuleRuleJob(monitorGroupId);
  }

  @Post('removeDeprecatedMonitorDataJob')
  @ApiQuery({ name: 'monitorGroupId', required: true, description: '分组名称', type: Number })
  @ApiQuery({ name: 'companyId', required: true, description: '企业名称', type: String })
  @ApiTags('其他内部接口')
  @ApiOperation({ description: '清除废弃的数据' })
  async removeDeprecatedMonitorDataJob(@Req() req: any, @Query('monitorGroupId') monitorGroupId: number, @Query('companyId') companyId: string) {
    return this.monitorJobService.removeDeprecatedMonitorDataJob(monitorGroupId, companyId);
  }

  @Get('/sendEmail')
  @ApiTags('其他内部接口')
  @ApiOperation({ description: '手动发送动态邮件' })
  @ApiQuery({ name: 'uniqueHashkeys', required: true, description: 'uniqueHashkey多个逗号分割', type: String })
  @ApiQuery({ name: 'orgId', required: true, description: 'orgId', type: Number })
  @ApiQuery({ name: 'email', required: true, description: '邮箱地址', type: String })
  public async sendEmail(@Req() req: any, @Query('uniqueHashkeys') uniqueHashkeys: string, @Query('orgId') orgId: number, @Query('email') email: string) {
    return this.internalService.sendEmailTest(uniqueHashkeys, orgId, email);
  }
}
