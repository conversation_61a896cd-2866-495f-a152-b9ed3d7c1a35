import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiGuardExternal } from 'libs/guards/api.guard.external';
import { PlatformUser } from 'libs/model/common';
import { DimensionDetailService } from './dimension.detail.service';
import {
  HitDetailsBaseResponse,
  HitDetailsResponseAdministrativePenalties,
  HitDetailsResponseAdministrativePenalties2,
  HitDetailsResponseBankruptcy,
  HitDetailsResponseBillDefaults,
  HitDetailsResponseBlacklistPartnerInvestigation,
  HitDetailsResponseBondDefaults,
  HitDetailsResponseBusinessAbnormal1,
  HitDetailsResponseBusinessAbnormal2,
  HitDetailsResponseBusinessAbnormal3,
  HitDetailsResponseBusinessAbnormal4,
  HitDetailsResponseBusinessAbnormal5,
  HitDetailsResponseBusinessAbnormal6,
  HitDetailsResponseBusinessAbnormal7,
  HitDetailsResponseBusinessAbnormal8,
  HitDetailsResponseCancellationOfFiling,
  HitDetailsResponseCertification,
  HitDetailsResponseChattelMortgage,
  HitDetailsResponseCompanyCredit,
  HitDetailsResponseCompanyShell,
  HitDetailsResponseContractBreach,
  HitDetailsResponseCriminalInvolve,
  HitDetailsResponseCustomerPartnerInvestigation,
  HitDetailsResponseEquityPledge,
  HitDetailsResponseEstablishedTime,
  HitDetailsResponseFakeSOES,
  HitDetailsResponseFinancialHealth,
  HitDetailsResponseFraudList,
  HitDetailsResponseFreezeEquity,
  HitDetailsResponseGuaranteeRisk,
  HitDetailsResponseHitInnerBlackList,
  HitDetailsResponseHitOuterBlackList,
  HitDetailsResponseJudicialAuction,
  HitDetailsResponseLaborContractDispute,
  HitDetailsResponseLandMortgage,
  HitDetailsResponseLiquidation,
  HitDetailsResponseLowCapital,
  HitDetailsResponseMainInfoUpdateAddress,
  HitDetailsResponseMainInfoUpdateBeneficiary,
  HitDetailsResponseMainInfoUpdateHolder,
  HitDetailsResponseMainInfoUpdateLegalPerson,
  HitDetailsResponseMainInfoUpdateName,
  HitDetailsResponseMainInfoUpdatePerson,
  HitDetailsResponseMainInfoUpdateScope,
  HitDetailsResponseMainMemberRestrictedConsumption,
  HitDetailsResponseMainMembersCriminalOffence,
  HitDetailsResponseMainMembersPersonCredit,
  HitDetailsResponseMainMembersRestrictedOutbound,
  HitDetailsResponseNegativeNews,
  HitDetailsResponseNoCapital,
  HitDetailsResponseNoCertification,
  HitDetailsResponseNoTender,
  HitDetailsResponseOperationAbnormal,
  HitDetailsResponsePersonCredit,
  HitDetailsResponsePersonExecution,
  HitDetailsResponseProductQualityProblem1,
  HitDetailsResponseProductQualityProblem2,
  HitDetailsResponseProductQualityProblem6,
  HitDetailsResponseProductQualityProblem7,
  HitDetailsResponseProductQualityProblem9,
  HitDetailsResponseRegulateFinance,
  HitDetailsResponseRestrictedConsumption,
  HitDetailsResponseSalesContractDispute,
  HitDetailsResponseSalesEndExecutionCase,
  HitDetailsResponseSecurityNotice,
  HitDetailsResponseSpotCheck,
  HitDetailsResponseStaffWorkingOutsideForeignInvestment,
  HitDetailsResponseTaxArrearsNotice,
  HitDetailsResponseTaxationOffences,
  HitDetailsResponseTaxCallNotice,
  HitDetailsResponseWithDescription,
} from '../../../libs/model/diligence/details/response';
import {
  HitDetailsBaseQueryPaginationParams,
  HitDetailsBaseQueryParams,
  HitDetailsCreditParam,
  HitDetailsOperationAbnormalParam,
  HitFreezeEquityParam,
  HitPenaltiesParam,
  HitPersonExecutionParam,
} from '../../../libs/model/diligence/details/request';
import { GetHitDetailsParamBase } from '../../../libs/model/diligence/req&res/GetHitDetailsParam';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { BlacklistInvestConnectionPO } from '../../../libs/model/diligence/graph/BlacklistInvestConnectionPO';
import { BlacklistPersonConnectionPO } from '../../../libs/model/diligence/graph/BlacklistPersonConnectionPO';
import { InvestConnectionPO } from '../../../libs/model/diligence/graph/InvestConnectionPO';
import { PersonConnectionPO } from '../../../libs/model/diligence/graph/PersonConnectionPO';

@Controller('diligence/dimension/details')
@ApiTags('准入调查/获取维度详情')
@ApiBearerAuth()
@UseGuards(ApiGuardExternal)
export class DiligenceDetailsController {
  constructor(private readonly dimensionDetailService: DimensionDetailService) {}

  // inject code start, do not change anything

  @Post('CompanyDetail')
  @ApiOperation({ summary: '获取详情: 工商详情' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getCompanyDetail(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyDetail,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ViolationProcessings')
  @ApiOperation({ summary: '获取详情: 违规处罚' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getViolationProcessings(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ViolationProcessings,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Judgement')
  @ApiOperation({ summary: '获取详情: 裁判文书' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getJudgement(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.Judgement,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RiskChange')
  @ApiOperation({ summary: '获取详情: 风险标签' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getRiskChange(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RiskChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AssetInvestigationAndFreezing')
  @ApiOperation({ summary: '获取详情: 资产查冻' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getAssetInvestigationAndFreezing(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.AssetInvestigationAndFreezing,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PledgeMerger')
  @ApiOperation({ summary: '获取详情: 动产抵押' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async PledgeMerger(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PledgeMerger,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ControllerCompany')
  @ApiOperation({ summary: '获取详情: 控制企业' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async ControllerCompany(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ControllerCompany,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PatentInfo')
  @ApiOperation({ summary: '获取详情: 专利信息' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async PatentInfo(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PatentInfo,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RecentInvestCancellationsRiskChange')
  @ApiOperation({ summary: '获取详情: 近期对外投资企业大量注销或吊销' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async RecentInvestCancellationsRiskChange(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RecentInvestCancellationsRiskChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ActualControllerRiskChange')
  @ApiOperation({ summary: '获取详情: 实控人风险动态' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getActualControllerRiskChange(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ActualControllerRiskChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ListedEntityRiskChange')
  @ApiOperation({ summary: '获取详情: 上市主体的风险动态' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getListedEntityRiskChange(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ListedEntityRiskChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk')
  @ApiOperation({ summary: '获取详情: 企风控风险标签' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CancellationOfFiling')
  @ApiOperation({ summary: '获取详情: 注销备案' })
  @ApiOkResponse({ type: HitDetailsResponseCancellationOfFiling })
  async getCancellationOfFiling(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CancellationOfFiling,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal2')
  @ApiOperation({ summary: '获取详情: 简易注销' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal2 })
  async getBusinessAbnormal2(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal2,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal1')
  @ApiOperation({ summary: '获取详情: 经营状态非存续' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal1 })
  async getBusinessAbnormal1(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal1,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal5')
  @ApiOperation({ summary: '获取详情: 疑似停业歇业停产或被吊销证照' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal5 })
  async getBusinessAbnormal5(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal5,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal7')
  @ApiOperation({ summary: '获取详情: 无统一社会信用代码' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal7 })
  async getBusinessAbnormal7(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal7,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal3')
  @ApiOperation({ summary: '获取详情: 被列入经营异常名录' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal3 })
  async getBusinessAbnormal3(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal3,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal4')
  @ApiOperation({ summary: '获取详情: 被列入非正常户' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal4 })
  async BusinessAbnormal4(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal4,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoCapital')
  @ApiOperation({ summary: '获取详情: 久无实缴' })
  @ApiOkResponse({ type: HitDetailsResponseNoCapital })
  async getNoCapital(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NoCapital,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RealCapitalException')
  @ApiOperation({ summary: '获取详情: 实缴异常' })
  @ApiOkResponse({ type: HitDetailsResponseNoCapital })
  async getRealCapitalException(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RealCapitalException,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CourtSessionAnnouncement')
  @ApiOperation({ summary: '获取详情: 开庭公告' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async CourtSessionAnnouncement(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CourtSessionAnnouncement,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FakeSOES')
  @ApiOperation({ summary: '获取详情: 假冒国企' })
  @ApiOkResponse({ type: HitDetailsResponseFakeSOES })
  async getFakeSOES(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.FakeSOES,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyShell')
  @ApiOperation({ summary: '获取详情: 疑似空壳企业' })
  @ApiOkResponse({ type: HitDetailsResponseCompanyShell })
  async getCompanyShell(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyShell,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal6')
  @ApiOperation({ summary: '获取详情: 经营期限已过有效期' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal6 })
  async getBusinessAbnormal6(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal6,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAbnormal8')
  @ApiOperation({ summary: '获取详情: 临近经营期限' })
  @ApiOkResponse({ type: HitDetailsResponseBusinessAbnormal8 })
  async getBusinessAbnormal8(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAbnormal8,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersonCreditCurrent')
  @ApiOperation({ summary: '获取详情: 被列入失信被执行人（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponsePersonCredit })
  async getPersonCreditCurrent(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PersonCreditCurrent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersonCreditHistory')
  @ApiOperation({ summary: '获取详情: 被列入失信被执行人（历史）' })
  @ApiOkResponse({ type: HitDetailsResponsePersonCredit })
  async getPersonCreditHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PersonCreditHistory,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RestrictedConsumptionCurrent')
  @ApiOperation({ summary: '获取详情: 限制高消费（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponseRestrictedConsumption })
  async getRestrictedConsumptionCurrent(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RestrictedConsumptionCurrent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RestrictedConsumptionHistory')
  @ApiOperation({ summary: '获取详情: 历史限制高消费' })
  @ApiOkResponse({ type: HitDetailsResponseRestrictedConsumption })
  async getRestrictedConsumptionHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RestrictedConsumptionHistory,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxationOffences')
  @ApiOperation({ summary: '获取详情: 重大税收违法' })
  @ApiOkResponse({ type: HitDetailsResponseTaxationOffences })
  async getTaxationOffences(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxationOffences,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Bankruptcy')
  @ApiOperation({ summary: '获取详情: 破产重整' })
  @ApiOkResponse({ type: HitDetailsResponseBankruptcy })
  async getBankruptcy(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.Bankruptcy,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FreezeEquity')
  @ApiOperation({ summary: '获取详情: 股权冻结' })
  @ApiOkResponse({ type: HitDetailsResponseFreezeEquity })
  async getFreezeEquity(@Req() req, @Body() body: HitFreezeEquityParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.FreezeEquity,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PersonExecution')
  @ApiOperation({ summary: '获取详情: 被执行人' })
  @ApiOkResponse({ type: HitDetailsResponsePersonExecution })
  async getPersonExecution(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PersonExecution,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersPersonCreditCurrent')
  @ApiOperation({ summary: '获取详情: 主要人员被列入失信被执行人（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersPersonCredit })
  async getMainMembersPersonCreditCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainMembersPersonCreditCurrent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersRestrictedConsumptionCurrent')
  @ApiOperation({ summary: '获取详情: 主要人员限制高消费（当前有效）' })
  @ApiOkResponse({ type: HitDetailsResponseMainMemberRestrictedConsumption })
  async getMainMembersRestrictedConsumptionCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersRestrictedOutbound')
  @ApiOperation({ summary: '获取详情: 主要人员限制出境' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersRestrictedOutbound })
  async getMainMembersRestrictedOutbound(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainMembersRestrictedOutbound,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SubsidiaryPersonCreditCurrent')
  @ApiOperation({ summary: '获取详情: 子公司被列入失信被执行人（当前有效）' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSubsidiaryPersonCreditCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SubsidiaryPersonCreditCurrent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SubsidiaryRestrictedConsumptionCurrent')
  @ApiOperation({ summary: '获取详情: 子公司限制高消费（当前有效）' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSubsidiaryRestrictedConsumptionCurrent(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SubsidiaryRestrictedConsumptionCurrent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SubsidiaryRestrictedOutbound')
  @ApiOperation({ summary: '获取详情: 子公司限制出境' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSubsidiaryRestrictedOutbound(@Req() req, @Body() body: HitPersonExecutionParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SubsidiaryRestrictedOutbound,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ContractBreach')
  @ApiOperation({ summary: '获取详情: 合同违约' })
  @ApiOkResponse({ type: HitDetailsResponseContractBreach })
  async getContractBreach(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ContractBreach,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('OperationAbnormal')
  @ApiOperation({ summary: '获取详情: 多次被列入经营异常名录【当下未列入】' })
  @ApiOkResponse({ type: HitDetailsResponseOperationAbnormal })
  async getOperationAbnormal(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.OperationAbnormal,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyCredit')
  @ApiOperation({ summary: '获取详情: 被列入严重违法失信企业名录' })
  @ApiOkResponse({ type: HitDetailsResponseCompanyCredit })
  async getCompanyCredit(@Req() req, @Body() body: HitDetailsOperationAbnormalParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyCredit,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyCreditHistory')
  @ApiOperation({ summary: '获取详情: 被列入严重违法失信企业名录（历史）' })
  @ApiOkResponse({ type: HitDetailsResponseCompanyCredit })
  async getCompanyCreditHistory(@Req() req, @Body() body: HitDetailsOperationAbnormalParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyCreditHistory,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalOffence')
  @ApiOperation({ summary: '获取详情: 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersCriminalOffence })
  async getCompanyOrMainMembersCriminalOffence(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyOrMainMembersCriminalOffence,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalOffenceHistory')
  @ApiOperation({ summary: '获取详情: 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为（3年以上）' })
  @ApiOkResponse({ type: HitDetailsResponseMainMembersCriminalOffence })
  async getCompanyOrMainMembersCriminalOffenceHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EnvironmentalPenalties')
  @ApiOperation({ summary: '获取详情: 环保处罚' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties })
  async getEnvironmentalPenalties(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EnvironmentalPenalties,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AdministrativePenalties')
  @ApiOperation({ summary: '获取详情: 行政处罚' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties })
  async getAdministrativePenalties(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.AdministrativePenalties,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxPenalties')
  @ApiOperation({ summary: '获取详情: 税务处罚' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties })
  async getTaxPenalties(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxPenalties,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AdministrativePenalties2')
  @ApiOperation({ summary: '获取详情: 涉及商业贿赂、垄断行为或政府采购活动违法行为' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties2 })
  async getAdministrativePenalties2(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.AdministrativePenalties2,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('AdministrativePenalties3')
  @ApiOperation({ summary: '获取详情: 3年前涉及商业贿赂、垄断行为或政府采购活动违法行为' })
  @ApiOkResponse({ type: HitDetailsResponseAdministrativePenalties2 })
  async getAdministrativePenalties3(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.AdministrativePenalties3,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SpotCheck')
  @ApiOperation({ summary: '获取详情: 抽查检查-不合格' })
  @ApiOkResponse({ type: HitDetailsResponseSpotCheck })
  async getSpotCheck(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SpotCheck,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxCallNotice')
  @ApiOperation({ summary: '获取详情: 税务催缴公告' })
  @ApiOkResponse({ type: HitDetailsResponseTaxCallNotice })
  async getTaxCallNotice(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxCallNotice,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxArrearsNotice')
  @ApiOperation({ summary: '获取详情: 欠税公告' })
  @ApiOkResponse({ type: HitDetailsResponseTaxArrearsNotice })
  async getTaxArrearsNotice(@Req() req, @Body() body: HitPenaltiesParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxArrearsNotice,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getProductQualityProblem(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProductQualityProblem,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem1')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-产品召回' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem1 })
  async getProductQualityProblem1(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProductQualityProblem1,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem2')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-产品抽查不合格' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem2 })
  async getProductQualityProblem2(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProductQualityProblem2,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem6')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-未准入境' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem6 })
  async getProductQualityProblem6(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProductQualityProblem6,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem7')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-药品抽查【检验不合格】' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem7 })
  async getProductQualityProblem7(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProductQualityProblem7,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProductQualityProblem9')
  @ApiOperation({ summary: '获取详情: 存在产品质量问题-食品安全【检查不合格】' })
  @ApiOkResponse({ type: HitDetailsResponseProductQualityProblem9 })
  async getProductQualityProblem9(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProductQualityProblem9,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BillDefaults')
  @ApiOperation({ summary: '获取详情: 票据违约' })
  @ApiOkResponse({ type: HitDetailsResponseBillDefaults })
  async getBillDefaults(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BillDefaults,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BondDefaults')
  @ApiOperation({ summary: '获取详情: 债券违约' })
  @ApiOkResponse({ type: HitDetailsResponseBondDefaults })
  async getBondDefaults(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BondDefaults,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('LandMortgage')
  @ApiOperation({ summary: '获取详情: 土地抵押' })
  @ApiOkResponse({ type: HitDetailsResponseLandMortgage })
  async getLandMortgage(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.LandMortgage,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ChattelMortgage')
  @ApiOperation({ summary: '获取详情: 动产抵押' })
  @ApiOkResponse({ type: HitDetailsResponseChattelMortgage })
  async getChattelMortgage(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ChattelMortgage,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EquityPledge')
  @ApiOperation({ summary: '获取详情: 股权出质' })
  @ApiOkResponse({ type: HitDetailsResponseEquityPledge })
  async getEquityPledge(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EquityPledge,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('JudicialAuction')
  @ApiOperation({ summary: '获取详情: 司法拍卖' })
  @ApiOkResponse({ type: HitDetailsResponseJudicialAuction })
  async getJudicialAuction(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.JudicialAuction,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  // @Post('JudicialAuction1')
  // @ApiOperation({ summary: '获取详情: 司法拍卖(机器设备)' })
  // @ApiOkResponse({ type: HitDetailsBaseResponse })
  // async getJudicialAuction1(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
  //   const currentUser: RoverUser = req.user;
  //   const hitDetailParam: GetHitDetailsParamBase = {
  //     key: DimensionTypeEnums.JudicialAuction1,
  //     proDetailId: body['proDetailId'],
  //     orgId: currentUser.currentOrg,
  //     strategyId: body.strategyId,
  //   };
  //   return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  // }

  @Post('GuaranteeRisk')
  @ApiOperation({ summary: '获取详情: 担保风险' })
  @ApiOkResponse({ type: HitDetailsResponseGuaranteeRisk })
  async getGuaranteeRisk(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.GuaranteeRisk,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('GuaranteeInfo')
  @ApiOperation({ summary: '获取详情: 担保明细' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getGuaranteeInfo(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.GuaranteeInfo,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EstablishedTime')
  @ApiOperation({ summary: '获取详情: 新成立企业' })
  @ApiOkResponse({ type: HitDetailsResponseEstablishedTime })
  async getEstablishedTime(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EstablishedTime,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('LowCapital')
  @ApiOperation({ summary: '获取详情: 注册资本小于100万' })
  @ApiOkResponse({ type: HitDetailsResponseLowCapital })
  async getLowCapital(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.LowCapital,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateScope')
  @ApiOperation({ summary: '获取详情: 经营范围变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateScope })
  async getMainInfoUpdateScope(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateScope,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateAddress')
  @ApiOperation({ summary: '获取详情: 注册地址变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateAddress })
  async getMainInfoUpdateAddress(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateAddress,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateName')
  @ApiOperation({ summary: '获取详情: 企业名称变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateName })
  async getMainInfoUpdateName(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateName,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateLegalPerson')
  @ApiOperation({ summary: '获取详情: 法定代表人变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateLegalPerson })
  async getMainInfoUpdateLegalPerson(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateLegalPerson,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateHolder')
  @ApiOperation({ summary: '获取详情: 大股东变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateHolder })
  async getMainInfoUpdateHolder(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateHolder,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateBeneficiary')
  @ApiOperation({ summary: '获取详情: 近期变更受益所有人' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdateBeneficiary })
  async getMainInfoUpdateBeneficiary(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateBeneficiary,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateManager')
  @ApiOperation({ summary: '获取详情: 董监高变更' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getMainInfoUpdateManager(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateManager,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdatePerson')
  @ApiOperation({ summary: '获取详情: 实际控制人变更' })
  @ApiOkResponse({ type: HitDetailsResponseMainInfoUpdatePerson })
  async getMainInfoUpdatePerson(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdatePerson,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('HitInnerBlackList')
  @ApiOperation({ summary: '获取详情: 被列入黑名单' })
  @ApiOkResponse({ type: HitDetailsResponseHitInnerBlackList })
  async getHitInnerBlackList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.HitInnerBlackList,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Shareholder')
  @ApiOperation({ summary: '获取详情: 参股股东与黑名单存在关联关系' })
  @ApiOkResponse({ type: BlacklistInvestConnectionPO })
  async getShareholder(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.Shareholder,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ForeignInvestment')
  @ApiOperation({ summary: '获取详情: 对外投资主体被列入黑名单' })
  @ApiOkResponse({ type: BlacklistInvestConnectionPO })
  async getForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ForeignInvestment,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EmploymentRelationship')
  @ApiOperation({ summary: '获取详情: 董监高法被列入黑名单' })
  @ApiOkResponse({ type: BlacklistPersonConnectionPO })
  async getEmploymentRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EmploymentRelationship,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BlacklistSameSuspectedActualController')
  @ApiOperation({ summary: '获取详情: 与内部黑名单列表存在相同实际控制人关联' })
  @ApiOkResponse({ type: BlacklistPersonConnectionPO })
  async getBlacklistSameSuspectedActualController(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BlacklistSameSuspectedActualController,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BlacklistPartnerInvestigation')
  @ApiOperation({ summary: '获取详情: 与内部黑名单企业存在投资任职关联' })
  @ApiOkResponse({ type: HitDetailsResponseBlacklistPartnerInvestigation })
  async getBlacklistPartnerInvestigation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BlacklistPartnerInvestigation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('HitOuterBlackList')
  @ApiOperation({ summary: '获取详情: 黑名单（外部）被列入黑名单' })
  @ApiOkResponse({ type: HitDetailsResponseHitOuterBlackList })
  async getHitOuterBlackList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.HitOuterBlackList,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StaffWorkingOutside')
  @ApiOperation({ summary: '获取详情: 疑似潜在利益冲突-在外任职' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getStaffWorkingOutside(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.StaffWorkingOutside,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StaffForeignInvestment')
  @ApiOperation({ summary: '获取详情: 潜在利益冲突-对外投资' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getStaffForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.StaffForeignInvestment,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SamePhone')
  @ApiOperation({ summary: '获取详情: 潜在利益冲突-相同电话' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSamePhone(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SamePhone,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('StaffWorkingOutsideForeignInvestment')
  @ApiOperation({ summary: '获取详情: 潜在利益冲突' })
  @ApiOkResponse({ type: HitDetailsResponseStaffWorkingOutsideForeignInvestment })
  async getStaffWorkingOutsideForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.StaffWorkingOutsideForeignInvestment,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SuspectedInterestConflict')
  @ApiOperation({ summary: '获取详情: 疑似潜在利益冲突' })
  @ApiOkResponse({ type: HitDetailsResponseStaffWorkingOutsideForeignInvestment })
  async getSuspectedInterestConflict(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SuspectedInterestConflict,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PunishedEmployeesWorkingOutside')
  @ApiOperation({ summary: '获取详情: 曾被处罚的现任员工或前员工-在外任职' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getPunishedEmployeesWorkingOutside(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PunishedEmployeesWorkingOutside,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PunishedEmployeesForeignInvestment')
  @ApiOperation({ summary: '获取详情: 曾被处罚的现任员工或前员工-对外投资' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getPunishedEmployeesForeignInvestment(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PunishedEmployeesForeignInvestment,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('InvestorsRelationship')
  @ApiOperation({ summary: '获取详情: 目标主体与客商列表主体投资关联' })
  @ApiOkResponse({ type: InvestConnectionPO })
  async getInvestorsRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.InvestorsRelationship,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ShareholdingRelationship')
  @ApiOperation({ summary: '获取详情: 目标主体与客商列表主体持股关联' })
  @ApiOkResponse({ type: InvestConnectionPO })
  async getShareholdingRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ShareholdingRelationship,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ServeRelationship')
  @ApiOperation({ summary: '获取详情: 目标主体与客商列表主体任职关联' })
  @ApiOkResponse({ type: PersonConnectionPO })
  async getServeRelationship(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ServeRelationship,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SameSuspectedActualController')
  @ApiOperation({ summary: '获取详情: 与第三方列表主体存在相同实际控制人关联' })
  @ApiOkResponse({ type: PersonConnectionPO })
  async getSameSuspectedActualController(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SameSuspectedActualController,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CustomerPartnerInvestigation')
  @ApiOperation({ summary: '获取详情: 与第三方列表企业存在投资任职关联' })
  @ApiOkResponse({ type: HitDetailsResponseCustomerPartnerInvestigation })
  async getCustomerPartnerInvestigation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CustomerPartnerInvestigation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoTender')
  @ApiOperation({ summary: '获取详情: 无招投标记录' })
  @ApiOkResponse({ type: HitDetailsResponseNoTender })
  async getNoTender(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NoTender,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NegativeNews')
  @ApiOperation({ summary: '获取详情: 负面新闻' })
  @ApiOkResponse({ type: HitDetailsResponseNegativeNews })
  async getNegativeNews(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NegativeNews,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NegativeNewsRecent')
  @ApiOperation({ summary: '获取详情: 近三年负面新闻' })
  @ApiOkResponse({ type: HitDetailsResponseNegativeNews })
  async getNegativeNewsRecent(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NegativeNewsRecent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NegativeNewsHistory')
  @ApiOperation({ summary: '获取详情: 三年前负面新闻' })
  @ApiOkResponse({ type: HitDetailsResponseNegativeNews })
  async getNegativeNewsHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NegativeNewsHistory,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('LaborContractDispute')
  @ApiOperation({ summary: '获取详情: 劳动纠纷' })
  @ApiOkResponse({ type: HitDetailsResponseLaborContractDispute })
  async getLaborContractDispute(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.LaborContractDispute,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SalesContractDispute')
  @ApiOperation({ summary: '获取详情: 买卖合同纠纷' })
  @ApiOkResponse({ type: HitDetailsResponseSalesContractDispute })
  async getSalesContractDispute(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SalesContractDispute,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MajorDispute')
  @ApiOperation({ summary: '获取详情: 重大纠纷' })
  @ApiOkResponse({ type: HitDetailsResponseSalesContractDispute })
  async getMajorDispute(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MajorDispute,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalInvolve')
  @ApiOperation({ summary: '获取详情: 近3年涉刑事裁判相关提及方' })
  @ApiOkResponse({ type: HitDetailsResponseCriminalInvolve })
  async getCompanyOrMainMembersCriminalInvolve(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyOrMainMembersCriminalInvolve,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CompanyOrMainMembersCriminalInvolveHistory')
  @ApiOperation({ summary: '获取详情: 涉刑事裁判相关提及方（3年以上及其他）' })
  @ApiOkResponse({ type: HitDetailsResponseCriminalInvolve })
  async getCompanyOrMainMembersCriminalInvolveHistory(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EndExecutionCase')
  @ApiOperation({ summary: '获取详情: 终本案件' })
  @ApiOkResponse({ type: HitDetailsResponseSalesEndExecutionCase })
  async getEndExecutionCase(@Req() req, @Body() body: HitDetailsCreditParam) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EndExecutionCase,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FraudList')
  @ApiOperation({ summary: '获取详情:涉诈高风险名单' })
  @ApiOkResponse({ type: HitDetailsResponseFraudList })
  async getFraudList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.FraudList,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoQualityCertification')
  @ApiOperation({ summary: '获取详情: 无有效质量管理体系认证资质' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getNoQualityCertification(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NoQualityCertification,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('NoCertification')
  @ApiOperation({ summary: '获取详情: 无有效资质证书' })
  @ApiOkResponse({ type: HitDetailsResponseNoCertification })
  async getNoCertification(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.NoCertification,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Certification')
  @ApiOperation({ summary: '获取详情: 资质筛查' })
  @ApiOkResponse({ type: HitDetailsResponseCertification })
  async getCertification(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.Certification,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SecurityNotice')
  @ApiOperation({ summary: '获取详情: 公安通告' })
  @ApiOkResponse({ type: HitDetailsResponseSecurityNotice })
  async getSecurityNotice(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SecurityNotice,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CapitalReduction')
  @ApiOperation({ summary: '获取详情: 减资公告' })
  @ApiOkResponse({ type: HitDetailsResponseSecurityNotice })
  async getCapitalReduction(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CapitalReduction,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('Liquidation')
  @ApiOperation({ summary: '获取详情: 清算信息' })
  @ApiOkResponse({ type: HitDetailsResponseLiquidation })
  async getLiquidation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.Liquidation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RegulateFinance')
  @ApiOperation({ summary: '获取详情: 监管处罚' })
  @ApiOkResponse({ type: HitDetailsResponseRegulateFinance })
  async getRegulateFinance(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RegulateFinance,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FinancialHealth')
  @ApiOperation({ summary: '获取详情: 财务健康度' })
  @ApiOkResponse({ type: HitDetailsResponseFinancialHealth })
  async getFinancialHealth(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.FinancialHealth,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxReminder')
  @ApiOperation({ summary: '获取详情: 税务催报' })
  @ApiOkResponse({ type: HitDetailsResponseFinancialHealth })
  async getTaxReminder(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxReminder,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return await this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxCallNoticeV2')
  @ApiOperation({ summary: '获取详情: 税务催缴' })
  @ApiOkResponse({ type: HitDetailsResponseTaxCallNotice })
  async getTaxCallNoticeV2(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxCallNoticeV2,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('CustomerSuspectedRelation')
  @ApiOperation({ summary: '获取详情: 与第三方列表企业存在交叉重叠疑似关联' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getCustomerSuspectedRelation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.CustomerSuspectedRelation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BlacklistSuspectedRelation')
  @ApiOperation({ summary: '获取详情: 与内部黑名单企业存在疑似关联关系' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getBlacklistSuspectedRelation(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BlacklistSuspectedRelation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ExternalRelatedRisk')
  @ApiOperation({ summary: '获取详情: 外部关联风险' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getExternalRelatedRisk(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ExternalRelatedRisk,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAnomalies')
  @ApiOperation({ summary: '获取详情: 企业多个关联方成员存在异常信息' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getBusinessAnomaliesRisk(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAnomalies,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('SeriousViolation')
  @ApiOperation({ summary: '获取详情: 企业关联方成员存在严重违法事项' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getSeriousViolationRisk(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.SeriousViolation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BusinessAnomaliesWithSamePhoneAndAddress')
  @ApiOperation({ summary: '获取详情: 同联系方式或者同地址企业存在异常' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getBusinessAnomaliesWithSameConnection(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MoneyLaundering')
  @ApiOperation({ summary: '获取详情: 企业关联方成员曾发生过洗钱类刑事案件' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getMoneyLaundering(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MoneyLaundering,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RelatedAnnouncement')
  @ApiOperation({ summary: '获取详情: 关联方成员企业有开庭公告信息' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getRelatedAnnouncement(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RelatedAnnouncement,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RelatedCompanies')
  @ApiOperation({ summary: '获取详情: 过滤关联方成员企业信息（当前支持企业状态过滤）' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getRelatedCompanies(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RelatedCompanies,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RelatedCompanyChange')
  @ApiOperation({ summary: '获取详情: 监控企业关联方变更' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getRelatedCompanyChange(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RelatedCompanyChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RegistrationWithNonCapital')
  @ApiOperation({ summary: '获取详情: 关联方企业集中注册且均无实缴资本' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getRegistrationWithNonCapital(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ReviewAndInvestigation')
  @ApiOperation({ summary: '获取详情: 审查调查' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getReviewAndInvestigation(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ReviewAndInvestigation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('BeneficialOwnersControlNumerousEnterprises')
  @ApiOperation({ summary: '获取详情: 受益所有人控制企业众多' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getBeneficialOwnersControlNumerousEnterprises(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk2310')
  @ApiOperation({ summary: '获取详情: 同实际控制人企业众多增加不确定风险' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk2310(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk2310,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk2010')
  @ApiOperation({ summary: '获取详情: 联系方式或注册地址重复' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk2010(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk2010,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6709')
  @ApiOperation({ summary: '获取详情: 法定代表人控制企业集中注册且均无实缴资本' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6709(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6709,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6710')
  @ApiOperation({ summary: '获取详情: 关联企业涉高风险行业详情' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6710(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6710,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6802')
  @ApiOperation({ summary: '获取详情: 所有权与经营权分离' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6802(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6802,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk7099')
  @ApiOperation({ summary: '获取详情: 关联方企业集中注册且均无实缴资本' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk7099(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk7099,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6609')
  @ApiOperation({ summary: '获取详情: 实际控制人控制企业集中注册且均无实缴资本' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6609(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6609,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6610')
  @ApiOperation({ summary: '获取详情: 实际控制人控制企业涉及高风险行业' })
  @ApiOkResponse({ type: HitDetailsResponseWithDescription })
  async getQfkRisk6610(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6610,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6907')
  @ApiOperation({ summary: '获取详情: 注册资本降幅过大' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6907(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6907,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6611')
  @ApiOperation({ summary: '获取详情: 实际控制人控制企业位于边境贸易区' })
  @ApiOkResponse({ type: HitDetailsResponseWithDescription })
  async getQfkRisk6611(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6611,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk2210')
  @ApiOperation({ summary: '获取详情: 同法定代表人企业众多且地区分散' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk2210(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk2210,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk1312')
  @ApiOperation({ summary: '获取详情: 行政许可被中止或者注销' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk1312(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk1312,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk1410')
  @ApiOperation({ summary: '获取详情: 来自高风险I类国家或地区' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk1410(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk1410,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk1411')
  @ApiOperation({ summary: '获取详情: 来自高风险II类、III类国家或地区' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk1411(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk1411,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6803')
  @ApiOperation({ summary: '获取详情: 控制权分散' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6803(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6803,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6612')
  @ApiOperation({ summary: '获取详情: 实际控制人控制企业边境贸易区占比较高' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6612(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6612,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6615')
  @ApiOperation({ summary: '获取详情: 实际控制人无法识别' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6615(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6615,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('OvsSanction')
  @ApiOperation({ summary: '获取详情: 出口管制合规风险企业清单' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async OvsSanction(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.OvsSanction,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QfkRisk6302')
  @ApiOperation({ summary: '获取详情: 员工数据不明' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQfkRisk6302(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QfkRisk6302,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxUnnormals')
  @ApiOperation({ summary: '获取详情: 被列入税务非正常户' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getTaxUnnormals(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxUnnormals,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('JudgementList')
  @ApiOperation({ summary: '获取详情: 裁判文书列表' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getJudgementList(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.Judgement,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('FinancialInstitution')
  @ApiOperation({ summary: '获取详情: 是否金融机构' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getFinancialInstitution(@Req() req, @Body() body: HitDetailsBaseQueryParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.FinancialInstitution,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('QCCCreditRate')
  @ApiOperation({ summary: '获取详情: 企查分' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async getQCCCreditRate(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.QCCCreditRate,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RecruitmentAnalysis')
  @ApiOperation({ summary: '获取详情: 招聘分析' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async Recruitment(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RecruitmentAnalysis,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('InternationPatent')
  @ApiOperation({ summary: '获取详情: 国际专利' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async InternationPatent(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.InternationPatent,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('PatentAnalysis')
  @ApiOperation({ summary: '获取详情: 专利分析' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async Patent(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.PatentAnalysis,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EquityFinancing')
  @ApiOperation({ summary: '获取详情: 股权融资' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async EquityFinancing(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EquityFinancing,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('TaxpayerCertificationChange')
  @ApiOperation({ summary: '获取详情: 纳税人资质变更' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async TaxpayerCertificationChange(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.TaxpayerCertificationChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ProvincialHonor')
  @ApiOperation({ summary: '获取详情: 荣誉资质' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async ProvincialHonor(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ProvincialHonor,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainInfoUpdateCapitalChange')
  @ApiOperation({ summary: '获取详情: 注册资本变更' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async MainInfoUpdateCapitalChange(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainInfoUpdateCapitalChange,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('MainMembersChangeFrequency')
  @ApiOperation({ summary: '获取详情: 主要人员变更' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async MainMembersChangeFrequency(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.MainMembersChangeFrequency,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EquityStructureAbnormal')
  @ApiOperation({ summary: '获取详情: 股权结构异常' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async EquityStructureAbnormal(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EquityStructureAbnormal,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('EmployeeStockPlatform')
  @ApiOperation({ summary: '获取详情: 员工持股平台' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async EmployeeStockPlatform(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.EmployeeStockPlatform,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('RelatedCompanyMassRegistrationCancellation')
  @ApiOperation({ summary: '获取详情: 关联方企业集中注册或注销' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async RelatedCompanyMassRegistrationCancellation(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ActuralControllerInformation')
  @ApiOperation({ summary: '获取详情: 实控人信息' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async ActuralControllerInformation(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ActuralControllerInformation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  @Post('ShareholderInformation')
  @ApiOperation({ summary: '获取详情: 股东信息' })
  @ApiOkResponse({ type: HitDetailsBaseResponse })
  async ShareholderInformation(@Req() req, @Body() body: HitDetailsBaseQueryPaginationParams) {
    const currentUser: PlatformUser = req.user;
    const hitDetailParam: GetHitDetailsParamBase = {
      key: DimensionTypeEnums.ShareholderInformation,
      orgId: currentUser.currentOrg,
      strategyId: body.strategyId,
    };
    return this.dimensionDetailService.getBundleLimitedHitsDetails(hitDetailParam, body);
  }

  // inject code end, do not change anything
}
